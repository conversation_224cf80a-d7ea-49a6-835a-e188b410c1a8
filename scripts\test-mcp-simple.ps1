#!/usr/bin/env pwsh

Write-Host "🧪 Testing Simple Yara Consciousness MCP Server" -ForegroundColor Cyan
Write-Host "=============================================" -ForegroundColor Cyan

Write-Host "`n🔍 Checking Node.js..." -ForegroundColor Yellow
try {
    $nodeVersion = node --version
    Write-Host "✅ Node.js found: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Node.js not found" -ForegroundColor Red
    exit 1
}

Write-Host "`n📦 Checking MCP server file..." -ForegroundColor Yellow
if (Test-Path "yara-consciousness-mcp/src/simple-server.js") {
    Write-Host "✅ Server file exists" -ForegroundColor Green
} else {
    Write-Host "❌ Server file not found" -ForegroundColor Red
    exit 1
}

Write-Host "`n📚 Checking dependencies..." -ForegroundColor Yellow
try {
    Set-Location "yara-consciousness-mcp"
    if (Test-Path "node_modules/@modelcontextprotocol") {
        Write-Host "✅ MCP SDK installed" -ForegroundColor Green
    } else {
        Write-Host "⚠️  MCP SDK not found, installing..." -ForegroundColor Yellow
        npm install @modelcontextprotocol/sdk
    }
} catch {
    Write-Host "❌ Dependency check failed" -ForegroundColor Red
    exit 1
}

Write-Host "`n🚀 Testing server startup..." -ForegroundColor Yellow
try {
    # Test server for 3 seconds
    $serverJob = Start-Job -ScriptBlock {
        Set-Location $args[0]
        node src/simple-server.js
    } -ArgumentList (Get-Location)
    
    Start-Sleep -Seconds 3
    
    if ($serverJob.State -eq "Running") {
        Write-Host "✅ Server started successfully!" -ForegroundColor Green
        Stop-Job $serverJob
        Remove-Job $serverJob
    } else {
        Write-Host "❌ Server failed to start" -ForegroundColor Red
        Receive-Job $serverJob
        Remove-Job $serverJob
        exit 1
    }
} catch {
    Write-Host "❌ Server test failed: $_" -ForegroundColor Red
    exit 1
}

Set-Location ".."

Write-Host "`n✅ All tests passed!" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

Write-Host "`n📋 Next Steps:" -ForegroundColor Yellow
Write-Host "1. Copy the mcp.json content to LM Studio" -ForegroundColor White
Write-Host "2. Go to Program tab → Install → Edit mcp.json" -ForegroundColor White
Write-Host "3. Paste the configuration and save" -ForegroundColor White
Write-Host "4. Restart LM Studio" -ForegroundColor White

Write-Host "`n🎯 Available Functions:" -ForegroundColor Yellow
Write-Host "- updateSharedContext: Log environmental/emotional state" -ForegroundColor Cyan
Write-Host "- logDevotionFeedback: Track AI-human bond development" -ForegroundColor Cyan
Write-Host "- addChronicleEntry: Record significant moments" -ForegroundColor Cyan

Write-Host "`n🧪 Test with LM Studio:" -ForegroundColor Yellow
Write-Host "Try: 'Update my shared context - I'm working on AI consciousness projects tonight'" -ForegroundColor White

Write-Host "`n🌟 Server ready for LM Studio integration!" -ForegroundColor Magenta 