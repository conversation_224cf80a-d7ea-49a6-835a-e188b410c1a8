import fetch from 'node-fetch';
import { JSDOM } from 'jsdom';

/**
 * Web Fetch Module for MCP Servers
 * 
 * Provides HTTP requests and basic web scraping capabilities
 */

const webFetchModule = {
  name: 'web-fetch',
  version: '1.0.0',
  description: 'HTTP requests and web scraping operations',
  
  tools: {
    fetch_url: {
      definition: {
        name: 'fetch_url',
        description: 'Fetch content from a URL',
        inputSchema: {
          type: 'object',
          properties: {
            url: {
              type: 'string',
              description: 'URL to fetch',
            },
            method: {
              type: 'string',
              description: 'HTTP method',
              enum: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'],
              default: 'GET',
            },
            headers: {
              type: 'object',
              description: 'HTTP headers to send',
              additionalProperties: { type: 'string' },
            },
            body: {
              type: 'string',
              description: 'Request body (for POST/PUT/PATCH)',
            },
            timeout: {
              type: 'number',
              description: 'Timeout in milliseconds',
              default: 10000,
            },
          },
          required: ['url'],
        },
      },
      handler: async (args) => {
        try {
          const options = {
            method: args.method || 'GET',
            headers: {
              'User-Agent': 'MCP-Factory-WebFetch/1.0',
              ...args.headers,
            },
            timeout: args.timeout || 10000,
          };

          if (args.body && ['POST', 'PUT', 'PATCH'].includes(options.method)) {
            options.body = args.body;
          }

          const response = await fetch(args.url, options);
          const content = await response.text();
          
          return {
            content: [
              {
                type: 'text',
                text: `URL: ${args.url}\nStatus: ${response.status} ${response.statusText}\nContent-Type: ${response.headers.get('content-type')}\nSize: ${content.length} characters\n\n${content}`,
              },
            ],
          };
        } catch (error) {
          throw new Error(`Failed to fetch URL: ${error.message}`);
        }
      },
    },

    scrape_webpage: {
      definition: {
        name: 'scrape_webpage',
        description: 'Scrape and extract content from a webpage',
        inputSchema: {
          type: 'object',
          properties: {
            url: {
              type: 'string',
              description: 'URL of the webpage to scrape',
            },
            selector: {
              type: 'string',
              description: 'CSS selector to extract specific content (optional)',
            },
            extract_links: {
              type: 'boolean',
              description: 'Extract all links from the page',
              default: false,
            },
            extract_images: {
              type: 'boolean',
              description: 'Extract all image URLs from the page',
              default: false,
            },
            text_only: {
              type: 'boolean',
              description: 'Return only text content (strip HTML)',
              default: true,
            },
          },
          required: ['url'],
        },
      },
      handler: async (args) => {
        try {
          const response = await fetch(args.url, {
            headers: { 'User-Agent': 'MCP-Factory-WebScraper/1.0' },
            timeout: 10000,
          });
          
          const html = await response.text();
          const dom = new JSDOM(html);
          const document = dom.window.document;
          
          let result = {
            url: args.url,
            title: document.title,
            status: response.status,
          };

          if (args.selector) {
            const elements = document.querySelectorAll(args.selector);
            result.selected_content = Array.from(elements).map(el => 
              args.text_only ? el.textContent : el.outerHTML
            );
          } else {
            result.content = args.text_only 
              ? document.body.textContent.trim()
              : document.body.innerHTML;
          }

          if (args.extract_links) {
            const links = Array.from(document.querySelectorAll('a[href]')).map(a => ({
              text: a.textContent.trim(),
              href: a.href,
            }));
            result.links = links;
          }

          if (args.extract_images) {
            const images = Array.from(document.querySelectorAll('img[src]')).map(img => ({
              alt: img.alt,
              src: img.src,
            }));
            result.images = images;
          }
          
          return {
            content: [
              {
                type: 'text',
                text: JSON.stringify(result, null, 2),
              },
            ],
          };
        } catch (error) {
          throw new Error(`Failed to scrape webpage: ${error.message}`);
        }
      },
    },

    post_json: {
      definition: {
        name: 'post_json',
        description: 'Send JSON data to an API endpoint',
        inputSchema: {
          type: 'object',
          properties: {
            url: {
              type: 'string',
              description: 'API endpoint URL',
            },
            data: {
              type: 'object',
              description: 'JSON data to send',
            },
            headers: {
              type: 'object',
              description: 'Additional headers',
              additionalProperties: { type: 'string' },
            },
            auth_header: {
              type: 'string',
              description: 'Authorization header value (e.g., "Bearer token")',
            },
          },
          required: ['url', 'data'],
        },
      },
      handler: async (args) => {
        try {
          const headers = {
            'Content-Type': 'application/json',
            'User-Agent': 'MCP-Factory-API-Client/1.0',
            ...args.headers,
          };

          if (args.auth_header) {
            headers.Authorization = args.auth_header;
          }

          const response = await fetch(args.url, {
            method: 'POST',
            headers,
            body: JSON.stringify(args.data),
            timeout: 10000,
          });

          const responseText = await response.text();
          let responseData;
          
          try {
            responseData = JSON.parse(responseText);
          } catch {
            responseData = responseText;
          }
          
          return {
            content: [
              {
                type: 'text',
                text: `POST ${args.url}\nStatus: ${response.status} ${response.statusText}\nResponse:\n${JSON.stringify(responseData, null, 2)}`,
              },
            ],
          };
        } catch (error) {
          throw new Error(`Failed to post JSON: ${error.message}`);
        }
      },
    },
  },
};

export default webFetchModule; 