#!/usr/bin/env node

import { promises as fs } from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

/**
 * 🤖 Claude Factory API
 * 
 * Simplified, working programmatic interface for <PERSON> to control the MCP Factory
 * Focus on reliable core functionality
 */

class ClaudeFactoryAPI {
  constructor() {
    this.factoryRoot = __dirname;
    this.templatesDir = path.join(this.factoryRoot, 'templates');
    this.modulesDir = path.join(this.factoryRoot, 'modules');
    this.examplesDir = path.join(this.factoryRoot, 'examples');
  }

  /**
   * Get comprehensive factory status
   */
  async getStatus() {
    const status = {
      healthy: true,
      timestamp: new Date().toISOString(),
      factory: {
        name: 'MCP Factory',
        version: '1.0.0',
        location: this.factoryRoot
      },
      components: {},
      errors: []
    };

    try {
      // Check templates
      const templates = await fs.readdir(this.templatesDir);
      status.components.templates = {
        count: templates.length,
        available: templates,
        details: await this.getTemplateDetails(templates)
      };

      // Check modules
      const modules = await fs.readdir(this.modulesDir);
      const validModules = [];
      const moduleDetails = {};
      
      for (const module of modules) {
        try {
          const modulePath = path.join(this.modulesDir, module, 'module.js');
          await fs.access(modulePath);
          validModules.push(module);
          
          // Get module package.json for details
          try {
            const packagePath = path.join(this.modulesDir, module, 'package.json');
            const packageContent = await fs.readFile(packagePath, 'utf8');
            const packageInfo = JSON.parse(packageContent);
            moduleDetails[module] = {
              description: packageInfo.description || 'No description',
              tools: packageInfo.mcpTools || 'Unknown'
            };
          } catch {
            moduleDetails[module] = { description: 'Basic module', tools: 'Unknown' };
          }
        } catch {
          // Skip invalid modules
        }
      }
      
      status.components.modules = {
        count: validModules.length,
        available: validModules,
        details: moduleDetails
      };

      // Check examples/servers
      const examples = await fs.readdir(this.examplesDir);
      const serverDetails = {};
      
      for (const example of examples) {
        try {
          const serverPath = path.join(this.examplesDir, example);
          const stat = await fs.stat(serverPath);
          if (stat.isDirectory()) {
            const packagePath = path.join(serverPath, 'package.json');
            try {
              const packageContent = await fs.readFile(packagePath, 'utf8');
              const packageInfo = JSON.parse(packageContent);
              serverDetails[example] = {
                description: packageInfo.description || 'MCP Server',
                hasConfig: await this.fileExists(path.join(serverPath, 'cursor-mcp-config.json'))
              };
            } catch {
              serverDetails[example] = { description: 'Unknown server', hasConfig: false };
            }
          }
        } catch {
          // Skip invalid directories
        }
      }
      
      status.components.servers = {
        count: examples.length,
        available: examples,
        details: serverDetails
      };

      return status;
    } catch (error) {
      status.healthy = false;
      status.errors.push(error.message);
      return status;
    }
  }

  /**
   * Create a new MCP server directly
   */
  async createServer(serverName, options = {}) {
    const {
      template = 'node-basic',
      description = `Claude-generated MCP Server: ${serverName}`,
      modules = []
    } = options;

    try {
      console.log(`🤖 Claude creating MCP server: ${serverName}`);
      
      // Validate server name
      if (!serverName || !/^[a-zA-Z0-9-_]+$/.test(serverName)) {
        throw new Error('Server name must contain only letters, numbers, hyphens, and underscores');
      }

      const serverDir = path.join(this.examplesDir, serverName);
      
      // Check if server already exists
      if (await this.fileExists(serverDir)) {
        throw new Error(`Server '${serverName}' already exists`);
      }

      // Create server directory
      await fs.mkdir(serverDir, { recursive: true });

      // Copy template
      const templateDir = path.join(this.templatesDir, template);
      if (!await this.fileExists(templateDir)) {
        throw new Error(`Template '${template}' not found`);
      }

      // Copy template files
      await this.copyDirectory(templateDir, serverDir);

      // Update package.json
      await this.updateServerPackageJson(serverDir, serverName, description);

      // Add modules if specified
      const loadedModules = [];
      if (modules.length > 0) {
        for (const moduleName of modules) {
          try {
            await this.addModuleToServer(serverDir, moduleName);
            loadedModules.push(moduleName);
          } catch (error) {
            console.warn(`⚠️ Could not load module '${moduleName}': ${error.message}`);
          }
        }
      }

      // Create Cursor config
      await this.createCursorConfig(serverDir, serverName);

      return {
        success: true,
        serverName,
        serverPath: serverDir,
        template,
        modules: loadedModules,
        message: `Server '${serverName}' created successfully`,
        instructions: {
          start: `cd ${serverDir} && npm install && npm start`,
          deploy: `Use cursor-mcp-config.json to add to Cursor settings`
        }
      };
    } catch (error) {
      return {
        success: false,
        serverName,
        error: error.message,
        message: `Failed to create server '${serverName}'`
      };
    }
  }

  /**
   * List available resources
   */
  async listResources() {
    const status = await this.getStatus();
    
    return {
      templates: status.components.templates?.available || [],
      modules: status.components.modules?.available || [],
      servers: status.components.servers?.available || [],
      details: {
        templates: status.components.templates?.details || {},
        modules: status.components.modules?.details || {},
        servers: status.components.servers?.details || {}
      }
    };
  }

  /**
   * Get detailed information about a specific server
   */
  async getServerInfo(serverName) {
    try {
      const serverPath = path.join(this.examplesDir, serverName);
      
      if (!await this.fileExists(serverPath)) {
        throw new Error(`Server '${serverName}' not found`);
      }

      const info = {
        serverName,
        serverPath,
        created: 'Unknown',
        lastModified: 'Unknown',
        files: {},
        modules: [],
        tools: [],
        cursorReady: false
      };

      // Get file stats
      try {
        const stat = await fs.stat(serverPath);
        info.created = stat.birthtime?.toISOString() || 'Unknown';
        info.lastModified = stat.mtime?.toISOString() || 'Unknown';
      } catch {
        // If stat fails, keep defaults
      }

      // Check files
      const packagePath = path.join(serverPath, 'package.json');
      const serverJsPath = path.join(serverPath, 'server.js');
      const configPath = path.join(serverPath, 'cursor-mcp-config.json');
      const modulesPath = path.join(serverPath, 'modules');

      info.files.packageJson = await this.fileExists(packagePath);
      info.files.serverJs = await this.fileExists(serverJsPath);
      info.files.cursorConfig = await this.fileExists(configPath);
      info.files.modulesDir = await this.fileExists(modulesPath);

      // Get package.json info
      if (info.files.packageJson) {
        try {
          const packageContent = await fs.readFile(packagePath, 'utf8');
          const packageInfo = JSON.parse(packageContent);
          info.description = packageInfo.description;
          info.version = packageInfo.version;
          info.dependencies = packageInfo.dependencies;
        } catch {
          // If reading fails, keep defaults
        }
      }

      // Get modules info
      if (info.files.modulesDir) {
        try {
          const moduleFiles = await fs.readdir(modulesPath);
          info.modules = moduleFiles.filter(f => f.endsWith('.js')).map(f => f.replace('.js', ''));
        } catch {
          // If reading fails, keep empty array
        }
      }

      // Check Cursor readiness
      info.cursorReady = info.files.packageJson && info.files.serverJs && info.files.cursorConfig;

      return {
        success: true,
        info,
        message: `Retrieved detailed information for server '${serverName}'`
      };
    } catch (error) {
      return {
        success: false,
        serverName,
        error: error.message,
        message: `Failed to get info for server '${serverName}'`
      };
    }
  }

  /**
   * Delete a server
   */
  async deleteServer(serverName) {
    try {
      const serverPath = path.join(this.examplesDir, serverName);
      
      if (!await this.fileExists(serverPath)) {
        throw new Error(`Server '${serverName}' not found`);
      }

      await fs.rm(serverPath, { recursive: true, force: true });
      
      return {
        success: true,
        serverName,
        message: `Server '${serverName}' deleted successfully`
      };
    } catch (error) {
      return {
        success: false,
        serverName,
        error: error.message,
        message: `Failed to delete server '${serverName}'`
      };
    }
  }

  /**
   * Validate a server (simplified)
   */
  async validateServer(serverName) {
    try {
      const serverPath = path.join(this.examplesDir, serverName);
      
      if (!await this.fileExists(serverPath)) {
        throw new Error(`Server '${serverName}' not found`);
      }

      const checks = [];
      let allValid = true;

      // Check package.json
      const packagePath = path.join(serverPath, 'package.json');
      if (await this.fileExists(packagePath)) {
        try {
          const packageContent = await fs.readFile(packagePath, 'utf8');
          JSON.parse(packageContent);
          checks.push('✅ package.json: valid');
        } catch {
          checks.push('❌ package.json: invalid JSON');
          allValid = false;
        }
      } else {
        checks.push('❌ package.json: missing');
        allValid = false;
      }

      // Check server.js
      const serverJsPath = path.join(serverPath, 'server.js');
      if (await this.fileExists(serverJsPath)) {
        checks.push('✅ server.js: exists');
      } else {
        checks.push('❌ server.js: missing');
        allValid = false;
      }

      // Check Cursor config
      const configPath = path.join(serverPath, 'cursor-mcp-config.json');
      if (await this.fileExists(configPath)) {
        checks.push('✅ cursor-mcp-config.json: exists');
      } else {
        checks.push('⚠️ cursor-mcp-config.json: missing (can regenerate)');
      }

      return {
        success: true,
        serverName,
        valid: allValid,
        checks,
        message: allValid ? `Server '${serverName}' is valid` : `Server '${serverName}' has validation issues`
      };
    } catch (error) {
      return {
        success: false,
        serverName,
        valid: false,
        error: error.message,
        message: `Failed to validate server '${serverName}'`
      };
    }
  }

  /**
   * Quick test of factory functionality
   */
  async quickTest() {
    const testName = `factory-test-${Date.now()}`;
    const results = {
      testName,
      timestamp: new Date().toISOString(),
      tests: [],
      allPassed: true,
      summary: {}
    };

    try {
      // Test 1: Status
      results.tests.push('🔍 Testing factory status...');
      const status = await this.getStatus();
      if (status.healthy) {
        results.tests.push('✅ Factory status: healthy');
      } else {
        results.tests.push(`❌ Factory status: ${status.errors?.join(', ') || 'unhealthy'}`);
        results.allPassed = false;
      }

      // Test 2: Create server
      results.tests.push('🏗️ Testing server creation...');
      const createResult = await this.createServer(testName, {
        description: 'Factory API test server',
        modules: ['file-ops']
      });
      if (createResult.success) {
        results.tests.push('✅ Server creation: success');
      } else {
        results.tests.push(`❌ Server creation: ${createResult.error}`);
        results.allPassed = false;
      }

      // Test 3: Validation
      if (createResult.success) {
        results.tests.push('🔍 Testing server validation...');
        const validateResult = await this.validateServer(testName);
        if (validateResult.success && validateResult.valid) {
          results.tests.push('✅ Server validation: passed');
        } else {
          results.tests.push(`❌ Server validation: ${validateResult.error || 'failed'}`);
          results.allPassed = false;
        }
      }

      // Test 4: List resources
      results.tests.push('📋 Testing resource listing...');
      const listResult = await this.listResources();
      if (listResult.servers && listResult.servers.includes(testName)) {
        results.tests.push('✅ Resource listing: server found');
      } else {
        results.tests.push('❌ Resource listing: server not found');
        results.allPassed = false;
      }

      // Test 5: Delete server
      results.tests.push('🧹 Testing server deletion...');
      const deleteResult = await this.deleteServer(testName);
      if (deleteResult.success) {
        results.tests.push('✅ Server deletion: success');
      } else {
        results.tests.push(`❌ Server deletion: ${deleteResult.error}`);
        results.allPassed = false;
      }

      // Calculate summary
      const totalTests = 5;
      const passedTests = results.tests.filter(t => t.includes('✅')).length;
      results.summary = {
        total: totalTests,
        passed: passedTests,
        failed: totalTests - passedTests,
        success: results.allPassed
      };

      return results;
    } catch (error) {
      results.allPassed = false;
      results.tests.push(`❌ Test framework error: ${error.message}`);
      return results;
    }
  }

  // Helper methods
  async fileExists(filePath) {
    try {
      await fs.access(filePath);
      return true;
    } catch {
      return false;
    }
  }

  async copyDirectory(src, dest) {
    const entries = await fs.readdir(src, { withFileTypes: true });
    
    for (const entry of entries) {
      const srcPath = path.join(src, entry.name);
      const destPath = path.join(dest, entry.name);
      
      if (entry.isDirectory()) {
        await fs.mkdir(destPath, { recursive: true });
        await this.copyDirectory(srcPath, destPath);
      } else {
        await fs.copyFile(srcPath, destPath);
      }
    }
  }

  async getTemplateDetails(templates) {
    const details = {};
    for (const template of templates) {
      try {
        const packagePath = path.join(this.templatesDir, template, 'package.json');
        const packageContent = await fs.readFile(packagePath, 'utf8');
        const packageInfo = JSON.parse(packageContent);
        details[template] = {
          description: packageInfo.description || 'MCP server template',
          language: template.includes('python') ? 'Python' : 'Node.js'
        };
      } catch {
        details[template] = { description: 'Template', language: 'Unknown' };
      }
    }
    return details;
  }

  async updateServerPackageJson(serverDir, serverName, description) {
    const packageJsonPath = path.join(serverDir, 'package.json');
    try {
      const packageContent = await fs.readFile(packageJsonPath, 'utf8');
      const packageInfo = JSON.parse(packageContent);
      
      packageInfo.name = serverName;
      packageInfo.description = description;
      
      await fs.writeFile(packageJsonPath, JSON.stringify(packageInfo, null, 2));
    } catch (error) {
      console.warn(`⚠️ Could not update package.json: ${error.message}`);
    }
  }

  async addModuleToServer(serverDir, moduleName) {
    const moduleDir = path.join(this.modulesDir, moduleName);
    const modulePath = path.join(moduleDir, 'module.js');
    
    if (!await this.fileExists(modulePath)) {
      throw new Error(`Module '${moduleName}' not found`);
    }

    // Copy module to server
    const serverModulesDir = path.join(serverDir, 'modules');
    await fs.mkdir(serverModulesDir, { recursive: true });
    
    const destModulePath = path.join(serverModulesDir, `${moduleName}.js`);
    await fs.copyFile(modulePath, destModulePath);

    // Update server.js to include the module (basic approach)
    const serverJsPath = path.join(serverDir, 'server.js');
    try {
      let serverContent = await fs.readFile(serverJsPath, 'utf8');
      
      // Add import
      const importLine = `import ${moduleName.replace(/-/g, '_')}Module from './modules/${moduleName}.js';`;
      if (!serverContent.includes(importLine)) {
        const importInsertPoint = serverContent.indexOf('\n/**');
        if (importInsertPoint > 0) {
          serverContent = serverContent.slice(0, importInsertPoint) + 
            '\n' + importLine + 
            serverContent.slice(importInsertPoint);
        }
      }

      // Add module loading
      const loadLine = `  server.loadModule(${moduleName.replace(/-/g, '_')}Module);`;
      if (!serverContent.includes(loadLine)) {
        const loadInsertPoint = serverContent.indexOf('  server.start()');
        if (loadInsertPoint > 0) {
          serverContent = serverContent.slice(0, loadInsertPoint) + 
            '  // Load modules\n' + loadLine + '\n\n' +
            serverContent.slice(loadInsertPoint);
        }
      }

      await fs.writeFile(serverJsPath, serverContent);
    } catch (error) {
      console.warn(`⚠️ Could not update server.js for module '${moduleName}': ${error.message}`);
    }
  }

  async createCursorConfig(serverDir, serverName) {
    const config = {
      mcpServers: {
        [serverName]: {
          command: "node",
          args: ["server.js"],
          cwd: serverDir.replace(/\\/g, '/')
        }
      }
    };

    const configPath = path.join(serverDir, 'cursor-mcp-config.json');
    await fs.writeFile(configPath, JSON.stringify(config, null, 2));
  }
}

// CLI Interface
async function main() {
  const api = new ClaudeFactoryAPI();
  const args = process.argv.slice(2);

  if (args.length === 0) {
    console.log(JSON.stringify({
      name: 'Claude Factory API',
      version: '1.0.0',
      description: 'AI-controlled modular MCP server factory',
      commands: {
        status: 'Get factory health and component inventory',
        list: 'List all templates, modules, and servers with details',
        create: 'create <name> [template] [modules...] - Build new MCP server',
        delete: 'delete <name> - Remove server and clean up files',
        validate: 'validate <name> - Check server integrity and configuration',
        test: 'Run comprehensive factory functionality test',
        info: 'info <name> - Get detailed information about specific server'
      },
      examples: {
        'Basic server': 'node claude-factory-api.js create my-server',
        'Web scraper': 'node claude-factory-api.js create scraper node-basic web-fetch',
        'Full featured': 'node claude-factory-api.js create dev-helper node-basic file-ops web-fetch system-utils',
        'Check status': 'node claude-factory-api.js status',
        'Run tests': 'node claude-factory-api.js test'
      },
      usage: 'node claude-factory-api.js <command> [args...]'
    }, null, 2));
    return;
  }

  const command = args[0];
  let result;

  try {
    switch (command) {
      case 'status':
        result = await api.getStatus();
        break;
        
      case 'list':
        result = await api.listResources();
        break;
        
      case 'create':
        if (args.length < 2) throw new Error('Server name required');
        result = await api.createServer(args[1], {
          template: args[2] || 'node-basic',
          modules: args.slice(3)
        });
        break;
        
      case 'delete':
        if (args.length < 2) throw new Error('Server name required');
        result = await api.deleteServer(args[1]);
        break;
        
      case 'validate':
        if (args.length < 2) throw new Error('Server name required');
        result = await api.validateServer(args[1]);
        break;
        
      case 'test':
        result = await api.quickTest();
        break;
        
      default:
        throw new Error(`Unknown command: ${command}`);
    }
    
    console.log(JSON.stringify(result, null, 2));
  } catch (error) {
    console.log(JSON.stringify({
      success: false,
      error: error.message,
      command,
      args: args.slice(1)
    }, null, 2));
    process.exit(1);
  }
}

// Export for programmatic use
export { ClaudeFactoryAPI };

// Ready for Claude to use!

// CLI execution
main().catch(error => {
  console.error('Claude Factory API Error:', error.message);
  process.exit(1);
}); 
 