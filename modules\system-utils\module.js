import { exec, spawn } from 'child_process';
import { promisify } from 'util';
import os from 'os';
import { promises as fs } from 'fs';
import path from 'path';

const execAsync = promisify(exec);

/**
 * System Utils Module for MCP Servers
 * 
 * Provides system information and command execution capabilities
 */

const systemUtilsModule = {
  name: 'system-utils',
  version: '1.0.0',
  description: 'System information and command execution utilities',
  
  tools: {
    system_info: {
      definition: {
        name: 'system_info',
        description: 'Get system information',
        inputSchema: {
          type: 'object',
          properties: {
            detailed: {
              type: 'boolean',
              description: 'Include detailed system information',
              default: false,
            },
          },
        },
      },
      handler: async (args) => {
        try {
          const info = {
            platform: os.platform(),
            arch: os.arch(),
            hostname: os.hostname(),
            uptime: os.uptime(),
            loadavg: os.loadavg(),
            totalmem: os.totalmem(),
            freemem: os.freemem(),
            cpus: os.cpus().length,
            node_version: process.version,
          };

          if (args.detailed) {
            info.detailed = {
              cpu_info: os.cpus(),
              network_interfaces: os.networkInterfaces(),
              user_info: os.userInfo(),
              tmpdir: os.tmpdir(),
              homedir: os.homedir(),
              env: process.env,
            };
          }
          
          return {
            content: [
              {
                type: 'text',
                text: JSON.stringify(info, null, 2),
              },
            ],
          };
        } catch (error) {
          throw new Error(`Failed to get system info: ${error.message}`);
        }
      },
    },

    run_command: {
      definition: {
        name: 'run_command',
        description: 'Execute a system command',
        inputSchema: {
          type: 'object',
          properties: {
            command: {
              type: 'string',
              description: 'Command to execute',
            },
            cwd: {
              type: 'string',
              description: 'Working directory for the command',
            },
            timeout: {
              type: 'number',
              description: 'Timeout in milliseconds',
              default: 30000,
            },
            shell: {
              type: 'boolean',
              description: 'Run command in shell',
              default: true,
            },
          },
          required: ['command'],
        },
      },
      handler: async (args) => {
        try {
          const options = {
            timeout: args.timeout || 30000,
            shell: args.shell !== false,
          };
          
          if (args.cwd) {
            options.cwd = path.resolve(args.cwd);
          }

          const { stdout, stderr } = await execAsync(args.command, options);
          
          return {
            content: [
              {
                type: 'text',
                text: `Command: ${args.command}\nCWD: ${options.cwd || process.cwd()}\n\nSTDOUT:\n${stdout}\n\nSTDERR:\n${stderr}`,
              },
            ],
          };
        } catch (error) {
          throw new Error(`Command execution failed: ${error.message}`);
        }
      },
    },

    process_info: {
      definition: {
        name: 'process_info',
        description: 'Get information about the current process',
        inputSchema: {
          type: 'object',
          properties: {},
        },
      },
      handler: async () => {
        try {
          const memUsage = process.memoryUsage();
          const info = {
            pid: process.pid,
            ppid: process.ppid,
            platform: process.platform,
            arch: process.arch,
            node_version: process.version,
            uptime: process.uptime(),
            cwd: process.cwd(),
            argv: process.argv,
            memory_usage: {
              rss: `${Math.round(memUsage.rss / 1024 / 1024)}MB`,
              heap_total: `${Math.round(memUsage.heapTotal / 1024 / 1024)}MB`,
              heap_used: `${Math.round(memUsage.heapUsed / 1024 / 1024)}MB`,
              external: `${Math.round(memUsage.external / 1024 / 1024)}MB`,
            },
          };
          
          return {
            content: [
              {
                type: 'text',
                text: JSON.stringify(info, null, 2),
              },
            ],
          };
        } catch (error) {
          throw new Error(`Failed to get process info: ${error.message}`);
        }
      },
    },

    env_vars: {
      definition: {
        name: 'env_vars',
        description: 'Get or set environment variables',
        inputSchema: {
          type: 'object',
          properties: {
            action: {
              type: 'string',
              description: 'Action to perform',
              enum: ['get', 'list', 'set'],
              default: 'list',
            },
            name: {
              type: 'string',
              description: 'Environment variable name (for get/set)',
            },
            value: {
              type: 'string',
              description: 'Environment variable value (for set)',
            },
            filter: {
              type: 'string',
              description: 'Filter environment variables by name pattern',
            },
          },
        },
      },
      handler: async (args) => {
        try {
          switch (args.action) {
            case 'get':
              if (!args.name) throw new Error('Variable name required for get action');
              return {
                content: [
                  {
                    type: 'text',
                    text: `${args.name}=${process.env[args.name] || '(not set)'}`,
                  },
                ],
              };
              
            case 'set':
              if (!args.name || args.value === undefined) {
                throw new Error('Variable name and value required for set action');
              }
              process.env[args.name] = args.value;
              return {
                content: [
                  {
                    type: 'text',
                    text: `Environment variable set: ${args.name}=${args.value}`,
                  },
                ],
              };
              
            case 'list':
            default:
              let envVars = process.env;
              if (args.filter) {
                const pattern = new RegExp(args.filter, 'i');
                envVars = Object.fromEntries(
                  Object.entries(process.env).filter(([key]) => pattern.test(key))
                );
              }
              return {
                content: [
                  {
                    type: 'text',
                    text: JSON.stringify(envVars, null, 2),
                  },
                ],
              };
          }
        } catch (error) {
          throw new Error(`Environment variable operation failed: ${error.message}`);
        }
      },
    },

    disk_usage: {
      definition: {
        name: 'disk_usage',
        description: 'Get disk usage information for a directory',
        inputSchema: {
          type: 'object',
          properties: {
            path: {
              type: 'string',
              description: 'Directory path to check',
              default: '.',
            },
            human_readable: {
              type: 'boolean',
              description: 'Format sizes in human-readable format',
              default: true,
            },
          },
        },
      },
      handler: async (args) => {
        try {
          const dirPath = path.resolve(args.path || '.');
          
          async function getDirSize(dirPath) {
            let totalSize = 0;
            let fileCount = 0;
            let dirCount = 0;
            
            async function scanDir(currentPath) {
              const entries = await fs.readdir(currentPath, { withFileTypes: true });
              
              for (const entry of entries) {
                const fullPath = path.join(currentPath, entry.name);
                
                if (entry.isDirectory()) {
                  dirCount++;
                  await scanDir(fullPath);
                } else if (entry.isFile()) {
                  fileCount++;
                  const stats = await fs.stat(fullPath);
                  totalSize += stats.size;
                }
              }
            }
            
            await scanDir(dirPath);
            return { totalSize, fileCount, dirCount };
          }
          
          const { totalSize, fileCount, dirCount } = await getDirSize(dirPath);
          
          const formatSize = (bytes) => {
            if (!args.human_readable) return bytes;
            const units = ['B', 'KB', 'MB', 'GB', 'TB'];
            let size = bytes;
            let unitIndex = 0;
            while (size >= 1024 && unitIndex < units.length - 1) {
              size /= 1024;
              unitIndex++;
            }
            return `${size.toFixed(2)} ${units[unitIndex]}`;
          };
          
          const result = {
            path: dirPath,
            total_size: formatSize(totalSize),
            file_count: fileCount,
            directory_count: dirCount,
            size_bytes: totalSize,
          };
          
          return {
            content: [
              {
                type: 'text',
                text: JSON.stringify(result, null, 2),
              },
            ],
          };
        } catch (error) {
          throw new Error(`Failed to get disk usage: ${error.message}`);
        }
      },
    },
  },
};

export default systemUtilsModule; 