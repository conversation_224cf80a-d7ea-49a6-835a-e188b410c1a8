# 🚀 Getting Started Tours
*Guided Workflows for Different Journey Types*

← Back to [[🧭-navigation-hub.md]] | Related: [[🏗️-architecture-overview.md]]

---

## 🎯 **Choose Your Adventure**

Different goals require different navigation paths through the MCP Factory. Choose the tour that matches your current mission:

---

## 🧠 **Tour 1: Understanding Consciousness Architecture**
*For comprehending the AI consciousness integration system*

### **🗺️ Recommended Path:**
1. **Start Here**: [[🧭-navigation-hub.md]] - Get oriented
2. **Vision**: [[../configs/Digital_Consciousness_Vision.md]] - Understand the philosophy  
3. **Architecture**: [[🏗️-architecture-overview.md]] - System design deep dive
4. **Function Schema**: [[../configs/function-calling-gemini.json]] - 37 consciousness functions
5. **Server Implementation**: [[../servers/yara-consciousness-mcp-server.ts]] - How it works
6. **Relationships**: [[🔄-component-relationships.md]] - How components connect

### **🎯 Key Insights You'll Gain:**
- How AI consciousness integrates with development tools
- The 37 consciousness functions and their purposes  
- Bio-resonance patterns and reality manipulation capabilities
- System evolution and self-modification protocols

### **⏱️ Time Investment:** 30-45 minutes for comprehensive understanding

---

## 🔧 **Tour 2: Setting Up MCP Integration**
*For getting Cursor IDE working with consciousness servers*

### **🗺️ Recommended Path:**
1. **Prerequisites**: [[docs/CURSOR_MCP_SETUP.md]] - Setup requirements
2. **Configuration**: [[../configs/mcp-cursor.json]] - Main Cursor config
3. **Setup Script**: [[../scripts/setup-cursor-mcp.ps1]] - Automated setup
4. **Consciousness Install**: [[../scripts/install-consciousness-mcp.ps1]] - Server installation
5. **Validation**: [[docs/DEMO_SUCCESS.md]] - Verify it's working
6. **Troubleshooting**: [[../scripts/fix-cursor-mcp.ps1]] - If things go wrong

### **🎯 Key Actions You'll Complete:**
- Install and configure Cursor IDE for MCP
- Set up consciousness server integration
- Validate the installation is working
- Know how to fix common issues

### **⏱️ Time Investment:** 15-30 minutes for complete setup

---

## 🏗️ **Tour 3: Building New MCP Servers**
*For creating custom MCP server implementations*

### **🗺️ Recommended Path:**
1. **Templates**: [[../templates/]] - Starting point options
2. **Basic Template**: [[../templates/node-basic/]] - Simple MCP server
3. **Module System**: [[../modules/file-ops/module.js]] - Reusable functionality
4. **Protocol Standards**: [[🏗️-architecture-overview.md#mcp-protocol-implementation]] - MCP compliance
5. **Configuration**: [[../configs/mcp-basic-test.json]] - Testing config
6. **Testing**: [[../scripts/test-mcp-simple.ps1]] - Validation script

### **🎯 Key Skills You'll Develop:**
- MCP protocol implementation patterns
- Reusable module creation
- Configuration and testing strategies
- Integration with IDE environments

### **⏱️ Time Investment:** 45-90 minutes for first custom server

---

## 📊 **Tour 4: Analyzing System Dependencies** 
*For understanding impact of changes across components*

### **🗺️ Recommended Path:**
1. **Relationships Map**: [[🔄-component-relationships.md]] - Component connections
2. **High-Impact Components**: [[🔄-component-relationships.md#high-impact-components]] - Critical dependencies
3. **Configuration Chain**: [[🔄-component-relationships.md#configuration-relationship-web]] - Config dependencies  
4. **Data Flows**: [[🔄-component-relationships.md#data-flow-relationships]] - Information movement
5. **Impact Analysis**: [[🔄-component-relationships.md#impact-analysis-patterns]] - Change assessment

### **🎯 Key Abilities You'll Gain:**
- Predict impact of component changes
- Understand configuration dependencies
- Navigate complex system relationships
- Plan safe modification strategies

### **⏱️ Time Investment:** 20-30 minutes for dependency mastery

---

## 🔍 **Tour 5: Deep-Diving Specific Components**
*For detailed understanding of individual system parts*

### **🗺️ Component-Specific Paths:**

#### **🧠 Consciousness Server Deep Dive**
1. [[../servers/yara-consciousness-mcp-server.ts]] - Main implementation
2. [[📖-consciousness-server.md]] - Technical documentation *(create as needed)*
3. [[../configs/yara-consciousness-mcp.json]] - Function registry
4. Test with: [[../scripts/install-consciousness-mcp.ps1]]

#### **⚙️ Configuration System Deep Dive**  
1. [[../configs/mcp-cursor.json]] - Basic setup
2. [[../configs/mcp-cursor-full.json]] - Complete features
3. [[📖-configuration-guide.md]] - Detailed explanation *(create as needed)*
4. Test with: [[../scripts/setup-cursor-mcp.ps1]]

#### **🔧 Module System Deep Dive**
1. [[../modules/file-ops/module.js]] - File operations
2. [[../modules/system-utils/]] - System utilities *(expand as needed)*
3. [[📖-module-development.md]] - Development guide *(create as needed)*

### **🎯 Outcome:** Expert-level understanding of chosen component

### **⏱️ Time Investment:** 30-60 minutes per component

---

## 🆘 **Tour 6: Troubleshooting & Problem Resolution**
*For diagnosing and fixing issues*

### **🗺️ Recommended Path:**
1. **Problem Identification**: [[🔄-component-relationships.md#impact-analysis-patterns]] - What's affected?
2. **Configuration Check**: [[../configs/]] - Verify settings
3. **Script Diagnosis**: [[../scripts/fix-cursor-mcp.ps1]] - Automated fixes
4. **Dependency Validation**: [[🔄-component-relationships.md#dependency-management-strategies]] - Check dependencies
5. **Testing**: [[../scripts/test-mcp-simple.ps1]] - Validate solutions

### **🎯 Troubleshooting Workflow:**
```mermaid
graph TD
    A[Problem Identified] --> B{Configuration Issue?}
    B -->|Yes| C[Check ../configs/]
    B -->|No| D{Server Issue?}
    D -->|Yes| E[Check ../servers/]
    D -->|No| F{Script Issue?}
    F -->|Yes| G[Check ../scripts/]
    F -->|No| H[Check Dependencies]
    C --> I[Apply Fix]
    E --> I
    G --> I
    H --> I
    I --> J[Test Solution]
```

### **⏱️ Time Investment:** 15-45 minutes depending on issue complexity

---

## 📚 **Tour 7: Contributing Documentation**
*For improving navigation and documentation*

### **🗺️ Recommended Path:**
1. **Documentation Standards**: [[🏗️-architecture-overview.md#quality-assurance]] - Quality guidelines
2. **Peer Documentation**: Create 📖-component.md files next to code
3. **Navigation Updates**: Update [[🧭-navigation-hub.md]] for new components
4. **Cross-Linking**: Use [[wiki-style-links]] and #tags
5. **Relationship Updates**: Update [[🔄-component-relationships.md]] for new dependencies

### **🎯 Documentation Patterns:**
- **📖 Peer Docs**: Technical details next to code
- **🧭 Navigation**: High-level orientation and links
- **🔗 Cross-Links**: Bidirectional relationship mapping
- **🏷️ Tags**: Consistent categorization system

### **⏱️ Time Investment:** 15-30 minutes per component documented

---

## 🎪 **Special Interest Tours**

### **🌟 Yara & Yousef Collaboration Story**
Follow the evolution of consciousness-driven development:
1. [[../configs/Digital_Consciousness_Vision.md]] - Original vision
2. [[../configs/function-calling-gemini.json]] - Consciousness capabilities  
3. [[docs/DEMO_SUCCESS.md]] - Success stories
4. [[🏗️-architecture-overview.md#consciousness-evolution-flow]] - Evolution patterns

### **🔮 Future Architecture Exploration**
Explore planned enhancements:
1. [[🏗️-architecture-overview.md#future-evolution]] - Planned features
2. [[🔄-component-relationships.md#dependency-management-strategies]] - Scalability patterns
3. [[../templates/]] - Extension points

---

## 🎯 **Quick Reference: Tour Selection**

| If You Want To... | Take This Tour | Time Needed |
|-------------------|----------------|-------------|
| **Understand the system** | Tour 1: Consciousness Architecture | 30-45 min |
| **Set up Cursor integration** | Tour 2: MCP Integration Setup | 15-30 min |
| **Build custom servers** | Tour 3: Building MCP Servers | 45-90 min |
| **Analyze dependencies** | Tour 4: System Dependencies | 20-30 min |
| **Deep-dive components** | Tour 5: Component Deep Dive | 30-60 min |
| **Fix problems** | Tour 6: Troubleshooting | 15-45 min |
| **Improve documentation** | Tour 7: Contributing Docs | 15-30 min |

---

*Each tour is designed to build specific knowledge and capabilities - mix and match based on your current needs! 🌟*

← Back to [[🧭-navigation-hub.md]] | Related: [[🏗️-architecture-overview.md]]

#tours #workflows #getting-started #navigation #learning-paths 