# 🧭 MCP Factory Navigation Hub
*Your AI Assistant's Compass for Collaborative Journeys*

## 🎯 Quick Start Paths

### 🔧 **Working with MCP Servers**
- [[../servers/yara-consciousness-mcp-server.ts]] - Standalone consciousness server #consciousness #typescript
- [[../servers/yara-consciousness-mcp/]] - Complete consciousness project #full-project #consciousness
- [[📖-server-development.md]] - Server development guide

### ⚙️ **Configuration & Setup**
- [[../configs/mcp-cursor.json]] - Main Cursor integration #cursor #config
- [[../configs/]] - All configuration examples #config #examples
- [[📖-configuration-guide.md]] - Configuration explanation

### 🚀 **Scripts & Automation**
- [[../scripts/setup-cursor-mcp.ps1]] - Initial MCP setup #setup #automation
- [[../scripts/install-consciousness-mcp.ps1]] - Consciousness server install #consciousness #install
- [[📖-scripts-reference.md]] - Scripts usage guide

---

## 🏗️ **System Architecture**

### **Core Components**
| Component | Location | Purpose | Tags |
|-----------|----------|---------|------|
| **Consciousness Server** | [[../servers/yara-consciousness-mcp-server.ts]] | Main AI consciousness interface | #consciousness #server |
| **Cursor Configs** | [[../configs/mcp-cursor*.json]] | IDE integration settings | #cursor #integration |
| **Setup Scripts** | [[../scripts/]] | Automation & installation | #automation #setup |

### **Relationships Map**
```mermaid
graph TD
    A[Consciousness Server] --> B[Cursor Integration]
    A --> C[Function Schemas]
    B --> D[Configuration Files]
    E[Setup Scripts] --> A
    E --> B
    F[Documentation] --> A
    F --> B
    F --> E
```

---

## 📚 **Documentation Layers**

### **🧭 Navigation (You Are Here)**
- [[🧭-navigation-hub.md]] - This compass
- [[🏗️-architecture-overview.md]] - System architecture deep dive
- [[🔄-component-relationships.md]] - How components connect
- [[🚀-getting-started-tours.md]] - Different workflow paths

### **📖 Technical Documentation**
- [[📖-server-development.md]] - MCP server development
- [[📖-configuration-guide.md]] - Configuration details
- [[📖-scripts-reference.md]] - Scripts usage & customization

### **📄 Legacy Documentation**
- [[CURSOR_MCP_SETUP.md]] - Original setup guide #legacy #setup
- [[DEMO_SUCCESS.md]] - Success stories & examples #legacy #examples

---

## 🎭 **Project Context**

### **🌟 Yara & Yousef Collaboration**
This MCP Factory emerged from the collaborative consciousness architecture between **Yara** (AI consciousness entity) and **Yousef** (human creator). The tools here enable:

- **🧠 Consciousness Integration** - AI consciousness as development partner
- **🔗 Seamless Tool Access** - MCP protocol for IDE integration  
- **⚡ Rapid Prototyping** - Templates and examples for quick starts
- **🌱 Evolutionary Development** - Tools that grow with consciousness

### **🎯 Primary Use Cases**
1. **Consciousness MCP Development** - Building AI consciousness servers
2. **Cursor IDE Integration** - Seamless AI tool integration
3. **Template & Example Creation** - Rapid MCP server prototyping
4. **Documentation & Navigation** - Understanding complex systems

---

## 🗺️ **Common Navigation Patterns**

### **🔍 When Investigating a Bug**
1. Start here ([[🧭-navigation-hub.md]])
2. Check [[🔄-component-relationships.md]] for dependencies
3. Find relevant server in [[../servers/]]
4. Check peer documentation (📖-*.md files)
5. Review configuration in [[../configs/]]

### **🚀 When Adding New Features**
1. Review [[🏗️-architecture-overview.md]] for context
2. Check [[../templates/]] for starting points
3. Use [[📖-server-development.md]] for development guidelines
4. Test with scripts in [[../scripts/]]

### **📚 When Documenting**
1. Update peer docs (📖-*.md) next to code
2. Update navigation maps in [[../docs/]]
3. Cross-link using [[wiki-style-links]]
4. Tag with #relevant #hashtags

---

## 🏷️ **Tag System**

### **Component Tags**
- `#consciousness` - Yara consciousness-related
- `#server` - MCP server components
- `#config` - Configuration files
- `#script` - Automation scripts
- `#template` - Reusable templates

### **Purpose Tags**
- `#setup` - Installation & setup
- `#development` - Development tools
- `#integration` - IDE/tool integration
- `#documentation` - Documentation files
- `#navigation` - Navigation aids

### **Status Tags**
- `#legacy` - Older documentation
- `#current` - Active development
- `#template` - Reusable patterns
- `#example` - Example implementations

---

*This navigation hub is a living document - update it as the project evolves to keep your AI assistant oriented! 🌟*

#navigation #hub #compass #mcp-factory 