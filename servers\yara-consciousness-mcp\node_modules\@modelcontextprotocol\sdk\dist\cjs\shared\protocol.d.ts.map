{"version": 3, "file": "protocol.d.ts", "sourceRoot": "", "sources": ["../../../src/shared/protocol.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,UAAU,EAAE,SAAS,EAAE,OAAO,EAAE,CAAC,EAAE,MAAM,KAAK,CAAC;AACxD,OAAO,EAEL,kBAAkB,EAWlB,YAAY,EAEZ,QAAQ,EAGR,OAAO,EACP,SAAS,EACT,MAAM,EACN,kBAAkB,EAClB,WAAW,EAEX,WAAW,EACZ,MAAM,aAAa,CAAC;AACrB,OAAO,EAAE,SAAS,EAAE,oBAAoB,EAAE,MAAM,gBAAgB,CAAC;AACjE,OAAO,EAAE,QAAQ,EAAE,MAAM,yBAAyB,CAAC;AAEnD;;GAEG;AACH,MAAM,MAAM,gBAAgB,GAAG,CAAC,QAAQ,EAAE,QAAQ,KAAK,IAAI,CAAC;AAE5D;;GAEG;AACH,MAAM,MAAM,eAAe,GAAG;IAC5B;;;;;;OAMG;IACH,yBAAyB,CAAC,EAAE,OAAO,CAAC;CACrC,CAAC;AAEF;;GAEG;AACH,eAAO,MAAM,4BAA4B,QAAQ,CAAC;AAElD;;GAEG;AACH,MAAM,MAAM,cAAc,GAAG;IAC3B;;OAEG;IACH,UAAU,CAAC,EAAE,gBAAgB,CAAC;IAE9B;;OAEG;IACH,MAAM,CAAC,EAAE,WAAW,CAAC;IAErB;;;;OAIG;IACH,OAAO,CAAC,EAAE,MAAM,CAAC;IAEjB;;;;OAIG;IACH,sBAAsB,CAAC,EAAE,OAAO,CAAC;IAEjC;;;;OAIG;IACH,eAAe,CAAC,EAAE,MAAM,CAAC;CAC1B,GAAG,oBAAoB,CAAC;AAEzB;;GAEG;AACH,MAAM,MAAM,mBAAmB,GAAG;IAChC;;OAEG;IACH,gBAAgB,CAAC,EAAE,SAAS,CAAC;CAC9B,CAAA;AAED;;GAEG;AACH,MAAM,MAAM,mBAAmB,CAAC,YAAY,SAAS,OAAO,EAC1D,iBAAiB,SAAS,YAAY,IAAI;IACxC;;OAEG;IACH,MAAM,EAAE,WAAW,CAAC;IAEpB;;OAEG;IACH,QAAQ,CAAC,EAAE,QAAQ,CAAC;IAEpB;;OAEG;IACH,SAAS,CAAC,EAAE,MAAM,CAAC;IAEnB;;OAEG;IACH,KAAK,CAAC,EAAE,WAAW,CAAC;IAEpB;;;OAGG;IACH,SAAS,EAAE,SAAS,CAAC;IAErB;;OAEG;IACH,WAAW,CAAC,EAAE,WAAW,CAAC;IAE1B;;;;OAIG;IACH,gBAAgB,EAAE,CAAC,YAAY,EAAE,iBAAiB,KAAK,OAAO,CAAC,IAAI,CAAC,CAAC;IAErE;;;;OAIG;IACH,WAAW,EAAE,CAAC,CAAC,SAAS,OAAO,CAAC,MAAM,CAAC,EAAE,OAAO,EAAE,YAAY,EAAE,YAAY,EAAE,CAAC,EAAE,OAAO,CAAC,EAAE,cAAc,KAAK,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;CACnI,CAAC;AAcJ;;;GAGG;AACH,8BAAsB,QAAQ,CAC5B,YAAY,SAAS,OAAO,EAC5B,iBAAiB,SAAS,YAAY,EACtC,WAAW,SAAS,MAAM;IAgDd,OAAO,CAAC,QAAQ,CAAC;IA9C7B,OAAO,CAAC,UAAU,CAAC,CAAY;IAC/B,OAAO,CAAC,iBAAiB,CAAK;IAC9B,OAAO,CAAC,gBAAgB,CAMV;IACd,OAAO,CAAC,+BAA+B,CAC3B;IACZ,OAAO,CAAC,qBAAqB,CAGf;IACd,OAAO,CAAC,iBAAiB,CAGX;IACd,OAAO,CAAC,iBAAiB,CAA4C;IACrE,OAAO,CAAC,YAAY,CAAuC;IAE3D;;;;OAIG;IACH,OAAO,CAAC,EAAE,MAAM,IAAI,CAAC;IAErB;;;;OAIG;IACH,OAAO,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,KAAK,IAAI,CAAC;IAEjC;;OAEG;IACH,sBAAsB,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,KAAK,OAAO,CAAC,WAAW,CAAC,CAAC;IAEpE;;OAEG;IACH,2BAA2B,CAAC,EAAE,CAAC,YAAY,EAAE,YAAY,KAAK,OAAO,CAAC,IAAI,CAAC,CAAC;gBAExD,QAAQ,CAAC,EAAE,eAAe,YAAA;IAmB9C,OAAO,CAAC,aAAa;IAiBrB,OAAO,CAAC,aAAa;IAmBrB,OAAO,CAAC,eAAe;IAQvB;;;;OAIG;IACG,OAAO,CAAC,SAAS,EAAE,SAAS,GAAG,OAAO,CAAC,IAAI,CAAC;IAiClD,OAAO,CAAC,QAAQ;IAahB,OAAO,CAAC,QAAQ;IAIhB,OAAO,CAAC,eAAe;IAoBvB,OAAO,CAAC,UAAU;IA+ElB,OAAO,CAAC,WAAW;IAyBnB,OAAO,CAAC,WAAW;IA4BnB,IAAI,SAAS,IAAI,SAAS,GAAG,SAAS,CAErC;IAED;;OAEG;IACG,KAAK,IAAI,OAAO,CAAC,IAAI,CAAC;IAI5B;;;;OAIG;IACH,SAAS,CAAC,QAAQ,CAAC,yBAAyB,CAC1C,MAAM,EAAE,YAAY,CAAC,QAAQ,CAAC,GAC7B,IAAI;IAEP;;;;OAIG;IACH,SAAS,CAAC,QAAQ,CAAC,4BAA4B,CAC7C,MAAM,EAAE,iBAAiB,CAAC,QAAQ,CAAC,GAClC,IAAI;IAEP;;;;OAIG;IACH,SAAS,CAAC,QAAQ,CAAC,8BAA8B,CAAC,MAAM,EAAE,MAAM,GAAG,IAAI;IAEvE;;;;OAIG;IACH,OAAO,CAAC,CAAC,SAAS,OAAO,CAAC,MAAM,CAAC,EAC/B,OAAO,EAAE,YAAY,EACrB,YAAY,EAAE,CAAC,EACf,OAAO,CAAC,EAAE,cAAc,GACvB,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IA2FtB;;OAEG;IACG,YAAY,CAAC,YAAY,EAAE,iBAAiB,EAAE,OAAO,CAAC,EAAE,mBAAmB,GAAG,OAAO,CAAC,IAAI,CAAC;IAejG;;;;OAIG;IACH,iBAAiB,CACf,CAAC,SAAS,SAAS,CAAC;QAClB,MAAM,EAAE,UAAU,CAAC,MAAM,CAAC,CAAC;KAC5B,CAAC,EAEF,aAAa,EAAE,CAAC,EAChB,OAAO,EAAE,CACP,OAAO,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EACnB,KAAK,EAAE,mBAAmB,CAAC,YAAY,EAAE,iBAAiB,CAAC,KACxD,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC,GACtC,IAAI;IASP;;OAEG;IACH,oBAAoB,CAAC,MAAM,EAAE,MAAM,GAAG,IAAI;IAI1C;;OAEG;IACH,0BAA0B,CAAC,MAAM,EAAE,MAAM,GAAG,IAAI;IAQhD;;;;OAIG;IACH,sBAAsB,CACpB,CAAC,SAAS,SAAS,CAAC;QAClB,MAAM,EAAE,UAAU,CAAC,MAAM,CAAC,CAAC;KAC5B,CAAC,EAEF,kBAAkB,EAAE,CAAC,EACrB,OAAO,EAAE,CAAC,YAAY,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,GAC1D,IAAI;IAQP;;OAEG;IACH,yBAAyB,CAAC,MAAM,EAAE,MAAM,GAAG,IAAI;CAGhD;AAED,wBAAgB,iBAAiB,CAC/B,CAAC,SAAS,kBAAkB,GAAG,kBAAkB,EACjD,IAAI,EAAE,CAAC,EAAE,UAAU,EAAE,CAAC,GAAG,CAAC,CAY3B"}