import { promises as fs } from 'fs';
import path from 'path';

/**
 * File Operations Module for MCP Servers
 * 
 * Provides basic file system operations that can be loaded into any MCP server.
 */

const fileOpsModule = {
  name: 'file-ops',
  version: '1.0.0',
  description: 'Basic file system operations',
  
  tools: {
    read_file: {
      definition: {
        name: 'read_file',
        description: 'Read the contents of a file',
        inputSchema: {
          type: 'object',
          properties: {
            path: {
              type: 'string',
              description: 'Path to the file to read',
            },
            encoding: {
              type: 'string',
              description: 'File encoding (default: utf8)',
              enum: ['utf8', 'ascii', 'base64', 'hex'],
              default: 'utf8',
            },
          },
          required: ['path'],
        },
      },
      handler: async (args) => {
        try {
          const filePath = path.resolve(args.path);
          const encoding = args.encoding || 'utf8';
          const content = await fs.readFile(filePath, encoding);
          
          return {
            content: [
              {
                type: 'text',
                text: `File: ${filePath}\nSize: ${content.length} ${encoding === 'utf8' ? 'characters' : 'bytes'}\n\n${content}`,
              },
            ],
          };
        } catch (error) {
          throw new Error(`Failed to read file: ${error.message}`);
        }
      },
    },

    write_file: {
      definition: {
        name: 'write_file',
        description: 'Write content to a file',
        inputSchema: {
          type: 'object',
          properties: {
            path: {
              type: 'string',
              description: 'Path to the file to write',
            },
            content: {
              type: 'string',
              description: 'Content to write to the file',
            },
            encoding: {
              type: 'string',
              description: 'File encoding (default: utf8)',
              enum: ['utf8', 'ascii', 'base64', 'hex'],
              default: 'utf8',
            },
            create_dirs: {
              type: 'boolean',
              description: 'Create parent directories if they don\'t exist',
              default: false,
            },
          },
          required: ['path', 'content'],
        },
      },
      handler: async (args) => {
        try {
          const filePath = path.resolve(args.path);
          const encoding = args.encoding || 'utf8';
          
          // Create parent directories if requested
          if (args.create_dirs) {
            const dir = path.dirname(filePath);
            await fs.mkdir(dir, { recursive: true });
          }
          
          await fs.writeFile(filePath, args.content, encoding);
          const stats = await fs.stat(filePath);
          
          return {
            content: [
              {
                type: 'text',
                text: `Successfully wrote to file: ${filePath}\nSize: ${stats.size} bytes\nModified: ${stats.mtime.toISOString()}`,
              },
            ],
          };
        } catch (error) {
          throw new Error(`Failed to write file: ${error.message}`);
        }
      },
    },

    list_directory: {
      definition: {
        name: 'list_directory',
        description: 'List contents of a directory',
        inputSchema: {
          type: 'object',
          properties: {
            path: {
              type: 'string',
              description: 'Path to the directory to list',
              default: '.',
            },
            show_hidden: {
              type: 'boolean',
              description: 'Include hidden files (starting with .)',
              default: false,
            },
            detailed: {
              type: 'boolean',
              description: 'Show detailed information (size, modified date, etc.)',
              default: false,
            },
          },
        },
      },
      handler: async (args) => {
        try {
          const dirPath = path.resolve(args.path || '.');
          const entries = await fs.readdir(dirPath);
          
          let items = entries;
          if (!args.show_hidden) {
            items = entries.filter(item => !item.startsWith('.'));
          }
          
          if (args.detailed) {
            const detailedItems = await Promise.all(
              items.map(async (item) => {
                const itemPath = path.join(dirPath, item);
                const stats = await fs.stat(itemPath);
                return {
                  name: item,
                  type: stats.isDirectory() ? 'directory' : 'file',
                  size: stats.size,
                  modified: stats.mtime.toISOString(),
                };
              })
            );
            
            return {
              content: [
                {
                  type: 'text',
                  text: `Directory: ${dirPath}\n\n${JSON.stringify(detailedItems, null, 2)}`,
                },
              ],
            };
          } else {
            return {
              content: [
                {
                  type: 'text',
                  text: `Directory: ${dirPath}\n\n${items.join('\n')}`,
                },
              ],
            };
          }
        } catch (error) {
          throw new Error(`Failed to list directory: ${error.message}`);
        }
      },
    },

    file_exists: {
      definition: {
        name: 'file_exists',
        description: 'Check if a file or directory exists',
        inputSchema: {
          type: 'object',
          properties: {
            path: {
              type: 'string',
              description: 'Path to check',
            },
          },
          required: ['path'],
        },
      },
      handler: async (args) => {
        try {
          const filePath = path.resolve(args.path);
          const stats = await fs.stat(filePath);
          
          return {
            content: [
              {
                type: 'text',
                text: JSON.stringify({
                  path: filePath,
                  exists: true,
                  type: stats.isDirectory() ? 'directory' : 'file',
                  size: stats.size,
                  modified: stats.mtime.toISOString(),
                }, null, 2),
              },
            ],
          };
        } catch (error) {
          if (error.code === 'ENOENT') {
            return {
              content: [
                {
                  type: 'text',
                  text: JSON.stringify({
                    path: path.resolve(args.path),
                    exists: false,
                  }, null, 2),
                },
              ],
            };
          }
          throw new Error(`Failed to check file: ${error.message}`);
        }
      },
    },
  },
};

export default fileOpsModule; 