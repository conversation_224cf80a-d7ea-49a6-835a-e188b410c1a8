#!/usr/bin/env node

import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import {
  CallToolRequestSchema,
  ErrorCode,
  ListToolsRequestSchema,
  McpError,
} from '@modelcontextprotocol/sdk/types.js';
import fileOpsModule from './modules/file-ops.js';

/**
 * Basic MCP Server Template
 * 
 * This template provides a minimal working MCP server that can be extended
 * with modules and additional functionality.
 */

class BasicMCPServer {
  constructor() {
    this.server = new Server(
      {
        name: 'demo-server',
        version: '1.0.0',
      },
      {
        capabilities: {
          tools: {},
        },
      }
    );

    this.tools = new Map();
    this.setupDefaultTools();
    this.setupHandlers();
  }

  /**
   * Setup default tools that come with the template
   */
  setupDefaultTools() {
    // Echo tool - simple example
    this.addTool({
      name: 'echo',
      description: 'Echo back the provided message',
      inputSchema: {
        type: 'object',
        properties: {
          message: {
            type: 'string',
            description: 'Message to echo back',
          },
        },
        required: ['message'],
      },
    }, async (args) => {
      return {
        content: [
          {
            type: 'text',
            text: `Echo: ${args.message}`,
          },
        ],
      };
    });

    // Server info tool
    this.addTool({
      name: 'server_info',
      description: 'Get information about this MCP server',
      inputSchema: {
        type: 'object',
        properties: {},
      },
    }, async () => {
      return {
        content: [
          {
            type: 'text',
            text: JSON.stringify({
              name: 'Demo MCP Server',
              version: '1.0.0',
              tools_count: this.tools.size,
              tools: Array.from(this.tools.keys()),
              uptime: process.uptime(),
              timestamp: new Date().toISOString(),
            }, null, 2),
          },
        ],
      };
    });
  }

  /**
   * Add a new tool to the server
   */
  addTool(toolDefinition, handler) {
    this.tools.set(toolDefinition.name, {
      definition: toolDefinition,
      handler: handler,
    });
  }

  /**
   * Setup MCP protocol handlers
   */
  setupHandlers() {
    // List available tools
    this.server.setRequestHandler(ListToolsRequestSchema, async () => {
      const tools = Array.from(this.tools.values()).map(tool => tool.definition);
      return { tools };
    });

    // Handle tool calls
    this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
      const { name, arguments: args } = request.params;
      
      if (!this.tools.has(name)) {
        throw new McpError(
          ErrorCode.MethodNotFound,
          `Tool "${name}" not found`
        );
      }

      try {
        const tool = this.tools.get(name);
        const result = await tool.handler(args || {});
        return result;
      } catch (error) {
        throw new McpError(
          ErrorCode.InternalError,
          `Tool execution failed: ${error.message}`
        );
      }
    });
  }

  /**
   * Start the server
   */
  async start() {
    const transport = new StdioServerTransport();
    await this.server.connect(transport);
    
    // Log to stderr so it doesn't interfere with MCP protocol
    console.error('🚀 Demo MCP Server started');
    console.error(`📊 Available tools: ${Array.from(this.tools.keys()).join(', ')}`);
  }

  /**
   * Load a module into the server
   */
  loadModule(moduleDefinition) {
    if (moduleDefinition.tools) {
      for (const [name, tool] of Object.entries(moduleDefinition.tools)) {
        this.addTool(tool.definition, tool.handler);
      }
    }
    
    console.error(`📦 Module loaded: ${moduleDefinition.name || 'unnamed'}`);
  }
}

// Start the server if this file is run directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const server = new BasicMCPServer();
  
  // Load modules
  server.loadModule(fileOpsModule);

  // Handle graceful shutdown
  process.on('SIGINT', () => {
    console.error('👋 Shutting down MCP server...');
    process.exit(0);
  });

  // Start the server
  server.start().catch((error) => {
    console.error('❌ Failed to start server:', error);
    process.exit(1);
  });
}

export { BasicMCPServer }; 