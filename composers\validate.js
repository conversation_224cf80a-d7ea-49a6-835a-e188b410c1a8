#!/usr/bin/env node

import { promises as fs } from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const factoryRoot = dirname(__dirname);

/**
 * MCP Server and Module Validator
 * 
 * Validates MCP servers and modules for correctness and compliance
 */

class MCPValidator {
  constructor() {
    this.modulesDir = path.join(factoryRoot, 'modules');
    this.templatesDir = path.join(factoryRoot, 'templates');
    this.examplesDir = path.join(factoryRoot, 'examples');
  }

  /**
   * Validate a complete MCP server
   */
  async validateServer(serverPath) {
    console.log(`🔍 Validating MCP server: ${serverPath}`);
    
    const results = {
      valid: true,
      errors: [],
      warnings: [],
      info: [],
    };

    try {
      // Check if server directory exists
      await fs.access(serverPath);
      results.info.push('✓ Server directory exists');
    } catch {
      results.errors.push('❌ Server directory not found');
      results.valid = false;
      return results;
    }

    // Validate package.json
    await this.validatePackageJson(serverPath, results);

    // Validate server.js
    await this.validateServerJs(serverPath, results);

    // Validate modules
    await this.validateServerModules(serverPath, results);

    // Validate Cursor configuration
    await this.validateCursorConfig(serverPath, results);

    // Summary
    if (results.valid) {
      console.log(`✅ Server validation passed`);
    } else {
      console.log(`❌ Server validation failed with ${results.errors.length} errors`);
    }

    return results;
  }

  /**
   * Validate package.json
   */
  async validatePackageJson(serverPath, results) {
    const packageJsonPath = path.join(serverPath, 'package.json');
    
    try {
      const packageJson = JSON.parse(await fs.readFile(packageJsonPath, 'utf8'));
      results.info.push('✓ package.json exists and is valid JSON');

      // Check required fields
      if (!packageJson.name) {
        results.errors.push('❌ package.json missing "name" field');
        results.valid = false;
      }

      if (!packageJson.type || packageJson.type !== 'module') {
        results.errors.push('❌ package.json must have "type": "module"');
        results.valid = false;
      }

      if (!packageJson.dependencies || !packageJson.dependencies['@modelcontextprotocol/sdk']) {
        results.errors.push('❌ package.json missing MCP SDK dependency');
        results.valid = false;
      }

      if (!packageJson.scripts || !packageJson.scripts.start) {
        results.warnings.push('⚠️  package.json missing "start" script');
      }

      results.info.push(`✓ Package name: ${packageJson.name}`);
      results.info.push(`✓ MCP SDK version: ${packageJson.dependencies?.['@modelcontextprotocol/sdk'] || 'not found'}`);

    } catch (error) {
      results.errors.push(`❌ package.json error: ${error.message}`);
      results.valid = false;
    }
  }

  /**
   * Validate server.js
   */
  async validateServerJs(serverPath, results) {
    const serverJsPath = path.join(serverPath, 'server.js');
    
    try {
      const serverContent = await fs.readFile(serverJsPath, 'utf8');
      results.info.push('✓ server.js exists');

      // Check for required imports
      const requiredImports = [
        '@modelcontextprotocol/sdk/server/index.js',
        '@modelcontextprotocol/sdk/server/stdio.js',
        '@modelcontextprotocol/sdk/types.js',
      ];

      for (const importPath of requiredImports) {
        if (serverContent.includes(importPath)) {
          results.info.push(`✓ Imports ${importPath}`);
        } else {
          results.errors.push(`❌ Missing import: ${importPath}`);
          results.valid = false;
        }
      }

      // Check for MCP server setup
      if (serverContent.includes('new Server(')) {
        results.info.push('✓ Creates MCP Server instance');
      } else {
        results.errors.push('❌ Missing MCP Server instantiation');
        results.valid = false;
      }

      if (serverContent.includes('StdioServerTransport')) {
        results.info.push('✓ Uses stdio transport');
      } else {
        results.warnings.push('⚠️  Not using stdio transport (may not work with Cursor)');
      }

      // Check for tool handlers
      if (serverContent.includes('ListToolsRequestSchema') && serverContent.includes('CallToolRequestSchema')) {
        results.info.push('✓ Implements tool request handlers');
      } else {
        results.errors.push('❌ Missing tool request handlers');
        results.valid = false;
      }

      // Check for module loading capability
      if (serverContent.includes('loadModule')) {
        results.info.push('✓ Supports module loading');
      } else {
        results.warnings.push('⚠️  No module loading support detected');
      }

    } catch (error) {
      results.errors.push(`❌ server.js error: ${error.message}`);
      results.valid = false;
    }
  }

  /**
   * Validate server modules
   */
  async validateServerModules(serverPath, results) {
    const modulesDir = path.join(serverPath, 'modules');
    
    try {
      await fs.access(modulesDir);
      const moduleFiles = await fs.readdir(modulesDir);
      
      if (moduleFiles.length === 0) {
        results.warnings.push('⚠️  No modules found');
        return;
      }

      results.info.push(`✓ Found ${moduleFiles.length} module files`);

      for (const moduleFile of moduleFiles) {
        if (moduleFile.endsWith('.js')) {
          const moduleName = path.basename(moduleFile, '.js');
          const moduleValidation = await this.validateModule(path.join(modulesDir, moduleFile));
          
          if (moduleValidation.valid) {
            results.info.push(`✓ Module ${moduleName} is valid`);
          } else {
            results.errors.push(`❌ Module ${moduleName} has errors`);
            results.errors.push(...moduleValidation.errors.map(e => `  ${e}`));
            results.valid = false;
          }
        }
      }
    } catch {
      results.warnings.push('⚠️  No modules directory found');
    }
  }

  /**
   * Validate Cursor configuration
   */
  async validateCursorConfig(serverPath, results) {
    const configPath = path.join(serverPath, 'cursor-mcp-config.json');
    
    try {
      const config = JSON.parse(await fs.readFile(configPath, 'utf8'));
      results.info.push('✓ Cursor configuration exists');

      if (config.mcpServers) {
        const serverNames = Object.keys(config.mcpServers);
        results.info.push(`✓ Configured servers: ${serverNames.join(', ')}`);

        for (const [serverName, serverConfig] of Object.entries(config.mcpServers)) {
          if (!serverConfig.command) {
            results.errors.push(`❌ Server ${serverName} missing command`);
            results.valid = false;
          }
          
          if (!serverConfig.args || !Array.isArray(serverConfig.args)) {
            results.errors.push(`❌ Server ${serverName} missing or invalid args`);
            results.valid = false;
          }

          if (serverConfig.command === 'node' && !serverConfig.args.includes('server.js')) {
            results.warnings.push(`⚠️  Server ${serverName} may not be pointing to server.js`);
          }
        }
      } else {
        results.errors.push('❌ Cursor config missing mcpServers section');
        results.valid = false;
      }
    } catch {
      results.warnings.push('⚠️  No Cursor configuration found');
    }
  }

  /**
   * Validate a single module
   */
  async validateModule(modulePath) {
    const results = {
      valid: true,
      errors: [],
      warnings: [],
      info: [],
    };

    try {
      const moduleContent = await fs.readFile(modulePath, 'utf8');
      results.info.push('✓ Module file exists');

      // Check for export
      if (moduleContent.includes('export default')) {
        results.info.push('✓ Has default export');
      } else {
        results.errors.push('❌ Missing default export');
        results.valid = false;
      }

      // Check module structure
      if (moduleContent.includes('name:') && moduleContent.includes('version:') && moduleContent.includes('tools:')) {
        results.info.push('✓ Has required module structure (name, version, tools)');
      } else {
        results.errors.push('❌ Missing required module structure');
        results.valid = false;
      }

      // Check for tool definitions
      const toolMatches = moduleContent.match(/\w+:\s*{[\s\S]*?definition:\s*{/g);
      if (toolMatches && toolMatches.length > 0) {
        results.info.push(`✓ Found ${toolMatches.length} tool definitions`);
      } else {
        results.warnings.push('⚠️  No tool definitions found');
      }

      // Check for handlers
      const handlerMatches = moduleContent.match(/handler:\s*async/g);
      if (handlerMatches && handlerMatches.length > 0) {
        results.info.push(`✓ Found ${handlerMatches.length} async handlers`);
      } else {
        results.errors.push('❌ No async handlers found');
        results.valid = false;
      }

    } catch (error) {
      results.errors.push(`❌ Module validation error: ${error.message}`);
      results.valid = false;
    }

    return results;
  }

  /**
   * Validate all modules in the modules directory
   */
  async validateAllModules() {
    console.log('🔍 Validating all modules...');
    
    try {
      const modules = await fs.readdir(this.modulesDir);
      let totalValid = 0;
      let totalInvalid = 0;

      for (const module of modules) {
        const modulePath = path.join(this.modulesDir, module, 'module.js');
        
        try {
          console.log(`\n📦 Validating module: ${module}`);
          const validation = await this.validateModule(modulePath);
          
          if (validation.valid) {
            console.log(`✅ ${module} is valid`);
            totalValid++;
          } else {
            console.log(`❌ ${module} has errors:`);
            validation.errors.forEach(error => console.log(`  ${error}`));
            totalInvalid++;
          }
        } catch {
          console.log(`⚠️  Module ${module} missing module.js file`);
          totalInvalid++;
        }
      }

      console.log(`\n📊 Validation Summary:`);
      console.log(`✅ Valid modules: ${totalValid}`);
      console.log(`❌ Invalid modules: ${totalInvalid}`);
      
      return totalInvalid === 0;
    } catch (error) {
      console.error('❌ Failed to validate modules:', error.message);
      return false;
    }
  }

  /**
   * Generate validation report
   */
  generateReport(results) {
    console.log('\n📋 Validation Report:');
    
    if (results.info.length > 0) {
      console.log('\nℹ️  Information:');
      results.info.forEach(info => console.log(`  ${info}`));
    }
    
    if (results.warnings.length > 0) {
      console.log('\n⚠️  Warnings:');
      results.warnings.forEach(warning => console.log(`  ${warning}`));
    }
    
    if (results.errors.length > 0) {
      console.log('\n❌ Errors:');
      results.errors.forEach(error => console.log(`  ${error}`));
    }
  }
}

// CLI interface
async function main() {
  const args = process.argv.slice(2);
  const validator = new MCPValidator();

  if (args.length === 0) {
    console.log(`
🔍 MCP Validator

Usage:
  node composers/validate.js <server-path>
  node composers/validate.js --modules
  node composers/validate.js --module <module-path>

Examples:
  node composers/validate.js examples/my-server
  node composers/validate.js --modules
  node composers/validate.js --module modules/file-ops/module.js

Options:
  --modules        Validate all modules in modules directory
  --module <path>  Validate a specific module
  --help           Show this help
`);
    return;
  }

  try {
    if (args[0] === '--modules') {
      const allValid = await validator.validateAllModules();
      process.exit(allValid ? 0 : 1);
    } else if (args[0] === '--module') {
      if (args.length < 2) {
        console.error('❌ Usage: node composers/validate.js --module <module-path>');
        process.exit(1);
      }
      const results = await validator.validateModule(args[1]);
      validator.generateReport(results);
      process.exit(results.valid ? 0 : 1);
    } else if (args[0] === '--help') {
      console.log('Help content shown above');
    } else {
      const serverPath = path.resolve(args[0]);
      const results = await validator.validateServer(serverPath);
      validator.generateReport(results);
      process.exit(results.valid ? 0 : 1);
    }
  } catch (error) {
    console.error('❌ Error:', error.message);
    process.exit(1);
  }
}

if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { MCPValidator }; 