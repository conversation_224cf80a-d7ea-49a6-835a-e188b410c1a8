#!/usr/bin/env node

import { promises as fs } from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const factoryRoot = dirname(__dirname);

/**
 * MCP Module Combiner
 * 
 * Combines multiple modules into a single MCP server
 */

class MCPModuleCombiner {
  constructor() {
    this.modulesDir = path.join(factoryRoot, 'modules');
    this.templatesDir = path.join(factoryRoot, 'templates');
    this.examplesDir = path.join(factoryRoot, 'examples');
  }

  /**
   * Combine modules into an existing server
   */
  async combineModules(serverName, moduleNames, options = {}) {
    const {
      serverDir = path.join(this.examplesDir, serverName),
      force = false,
    } = options;

    console.log(`🧩 Combining modules into server: ${serverName}`);
    console.log(`📦 Modules: ${moduleNames.join(', ')}`);

    // Check if server exists
    try {
      await fs.access(serverDir);
    } catch {
      throw new Error(`Server "${serverName}" not found at ${serverDir}. Create it first with: node factory/new-server.js ${serverName}`);
    }

    // Validate modules exist
    const validModules = await this.validateModules(moduleNames);
    if (validModules.length !== moduleNames.length) {
      const missing = moduleNames.filter(name => !validModules.includes(name));
      throw new Error(`Modules not found: ${missing.join(', ')}`);
    }

    // Update package.json dependencies
    await this.updateDependencies(serverDir, validModules);

    // Copy modules
    await this.copyModules(serverDir, validModules);

    // Update server.js
    await this.updateServerCode(serverDir, serverName, validModules);

    // Update Cursor configuration
    await this.updateCursorConfig(serverDir, serverName);

    console.log(`✅ Successfully combined ${validModules.length} modules into ${serverName}`);
    console.log(`📁 Server location: ${serverDir}`);
    console.log(`\n🚀 To run the server:`);
    console.log(`   cd ${path.relative(process.cwd(), serverDir)}`);
    console.log(`   npm install`);
    console.log(`   npm start`);

    return serverDir;
  }

  /**
   * Validate that modules exist
   */
  async validateModules(moduleNames) {
    const validModules = [];
    
    for (const moduleName of moduleNames) {
      const modulePath = path.join(this.modulesDir, moduleName, 'module.js');
      try {
        await fs.access(modulePath);
        validModules.push(moduleName);
        console.log(`✓ Module found: ${moduleName}`);
      } catch {
        console.warn(`⚠️  Module not found: ${moduleName}`);
      }
    }

    return validModules;
  }

  /**
   * Update package.json with module dependencies
   */
  async updateDependencies(serverDir, moduleNames) {
    const packageJsonPath = path.join(serverDir, 'package.json');
    
    try {
      const packageJson = JSON.parse(await fs.readFile(packageJsonPath, 'utf8'));
      
      // Add dependencies based on modules
      if (!packageJson.dependencies) packageJson.dependencies = {};
      
      for (const moduleName of moduleNames) {
        switch (moduleName) {
          case 'web-fetch':
            packageJson.dependencies['node-fetch'] = '^3.3.2';
            packageJson.dependencies['jsdom'] = '^23.0.1';
            break;
          case 'system-utils':
            // No additional dependencies needed (built-in Node.js modules)
            break;
          case 'file-ops':
            // No additional dependencies needed (built-in Node.js modules)
            break;
        }
      }
      
      await fs.writeFile(packageJsonPath, JSON.stringify(packageJson, null, 2));
      console.log(`📝 Updated package.json dependencies`);
    } catch (error) {
      console.warn(`⚠️  Could not update package.json: ${error.message}`);
    }
  }

  /**
   * Copy modules to server directory
   */
  async copyModules(serverDir, moduleNames) {
    const modulesDir = path.join(serverDir, 'modules');
    await fs.mkdir(modulesDir, { recursive: true });

    for (const moduleName of moduleNames) {
      const srcPath = path.join(this.modulesDir, moduleName, 'module.js');
      const destPath = path.join(modulesDir, `${moduleName}.js`);
      
      await fs.copyFile(srcPath, destPath);
      console.log(`📦 Module copied: ${moduleName}`);
    }
  }

  /**
   * Update server.js to include modules
   */
  async updateServerCode(serverDir, serverName, moduleNames) {
    const serverPath = path.join(serverDir, 'server.js');
    let serverContent = await fs.readFile(serverPath, 'utf8');

    // Generate import statements
    const moduleImports = moduleNames.map(name => 
      `import ${this.toCamelCase(name)}Module from './modules/${name}.js';`
    ).join('\n');

    // Generate module loading statements
    const moduleLoads = moduleNames.map(name => 
      `  server.loadModule(${this.toCamelCase(name)}Module);`
    ).join('\n');

    // Check if imports already exist
    if (!serverContent.includes('import') || !serverContent.includes('Module from \'./modules/')) {
      // Add imports after MCP SDK imports
      const importInsertPoint = serverContent.indexOf('\n/**');
      serverContent = serverContent.slice(0, importInsertPoint) + 
        '\n' + moduleImports + 
        serverContent.slice(importInsertPoint);
    } else {
      // Replace existing imports
      const importRegex = /import.*Module from '\.\/modules\/.*';/g;
      serverContent = serverContent.replace(importRegex, '');
      const importInsertPoint = serverContent.indexOf('\n/**');
      serverContent = serverContent.slice(0, importInsertPoint) + 
        '\n' + moduleImports + 
        serverContent.slice(importInsertPoint);
    }

    // Add/replace module loading
    if (!serverContent.includes('server.loadModule')) {
      const loadInsertPoint = serverContent.indexOf('  server.start()');
      serverContent = serverContent.slice(0, loadInsertPoint) + 
        '  // Load modules\n' + moduleLoads + '\n\n' +
        serverContent.slice(loadInsertPoint);
    } else {
      // Replace existing module loads
      const loadRegex = /  \/\/ Load modules[\s\S]*?(?=  server\.start\(\))/;
      const replacement = '  // Load modules\n' + moduleLoads + '\n\n';
      serverContent = serverContent.replace(loadRegex, replacement);
    }

    await fs.writeFile(serverPath, serverContent);
    console.log(`🔧 Updated server.js with ${moduleNames.length} modules`);
  }

  /**
   * Update Cursor MCP configuration
   */
  async updateCursorConfig(serverDir, serverName) {
    const configPath = path.join(serverDir, 'cursor-mcp-config.json');
    const config = {
      mcpServers: {
        [serverName]: {
          command: "node",
          args: ["server.js"],
          cwd: serverDir
        }
      }
    };

    await fs.writeFile(configPath, JSON.stringify(config, null, 2));
    console.log(`⚙️  Updated Cursor configuration`);
  }

  /**
   * Convert kebab-case to camelCase
   */
  toCamelCase(str) {
    return str.replace(/-([a-z])/g, (g) => g[1].toUpperCase());
  }

  /**
   * List available modules
   */
  async listAvailableModules() {
    try {
      const modules = await fs.readdir(this.modulesDir);
      console.log('📦 Available modules:');
      
      for (const module of modules) {
        const modulePath = path.join(this.modulesDir, module, 'module.js');
        try {
          await fs.access(modulePath);
          console.log(`   ✓ ${module}`);
        } catch {
          console.log(`   ⚠️  ${module} (missing module.js)`);
        }
      }
    } catch (error) {
      console.error('❌ Failed to list modules:', error.message);
    }
  }

  /**
   * Create a new combined server from scratch
   */
  async createCombinedServer(serverName, moduleNames, options = {}) {
    const {
      template = 'node-basic',
      description = `MCP server with ${moduleNames.join(', ')} modules`,
    } = options;

    // Import and use the factory to create the base server
    const { MCPServerFactory } = await import('../factory/new-server.js');
    const factory = new MCPServerFactory();
    
    const serverDir = await factory.createServer(serverName, {
      template,
      modules: [], // We'll add modules separately for better control
      description,
    });

    // Now combine the modules
    await this.combineModules(serverName, moduleNames, { serverDir });

    return serverDir;
  }
}

// CLI interface
async function main() {
  const args = process.argv.slice(2);
  const combiner = new MCPModuleCombiner();

  if (args.length === 0) {
    console.log(`
🧩 MCP Module Combiner

Usage:
  node composers/combine.js <server-name> <module1> [module2] [...]
  node composers/combine.js --list-modules
  node composers/combine.js --create <server-name> <module1> [module2] [...]

Examples:
  node composers/combine.js my-server file-ops web-fetch
  node composers/combine.js web-scraper web-fetch file-ops system-utils
  node composers/combine.js --create dev-server file-ops web-fetch system-utils

Options:
  --list-modules    List all available modules
  --create         Create a new server and add modules
  --help           Show this help
`);
    return;
  }

  try {
    if (args[0] === '--list-modules') {
      await combiner.listAvailableModules();
    } else if (args[0] === '--create') {
      if (args.length < 3) {
        console.error('❌ Usage: node composers/combine.js --create <server-name> <module1> [module2] [...]');
        process.exit(1);
      }
      const serverName = args[1];
      const moduleNames = args.slice(2);
      await combiner.createCombinedServer(serverName, moduleNames);
    } else if (args[0] === '--help') {
      console.log('Help content shown above');
    } else {
      if (args.length < 2) {
        console.error('❌ Usage: node composers/combine.js <server-name> <module1> [module2] [...]');
        process.exit(1);
      }
      const serverName = args[0];
      const moduleNames = args.slice(1);
      await combiner.combineModules(serverName, moduleNames);
    }
  } catch (error) {
    console.error('❌ Error:', error.message);
    process.exit(1);
  }
}

if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { MCPModuleCombiner }; 