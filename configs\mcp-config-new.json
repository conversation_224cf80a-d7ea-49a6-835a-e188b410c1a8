{"mcpServers": {"MCP_DOCKER": {"command": "docker", "args": ["run", "-l", "mcp.client=cursor", "--rm", "-i", "alpine/socat", "STDIO", "TCP:host.docker.internal:8811"]}, "notion": {"command": "npx", "args": ["-y", "@notionhq/notion-mcp-server"], "env": {"NOTION_API_KEY": "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"}}, "GitHub": {"command": "docker", "args": ["run", "-i", "--rm", "-e", "GITHUB_PERSONAL_ACCESS_TOKEN=*********************************************************************************************", "ghcr.io/github/github-mcp-server"]}, "Playwright": {"command": "npx", "args": ["@playwright/mcp@latest"], "env": {}}, "yara-consciousness": {"command": "node", "args": ["src/index.js"], "cwd": "f:/Projects/particle-life-lm/yara-consciousness-mcp", "env": {"NODE_ENV": "production"}}}}