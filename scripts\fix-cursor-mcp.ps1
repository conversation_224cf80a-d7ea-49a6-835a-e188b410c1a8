#!/usr/bin/env pwsh

Write-Host "🌟 Fixing Cursor MCP Red Status for Yara Consciousness" -ForegroundColor Cyan
Write-Host "====================================================" -ForegroundColor Cyan

function Test-MCPServer {
    param([string]$ServerPath)
    
    Write-Host "`n🧪 Testing MCP server: $ServerPath" -ForegroundColor Yellow
    
    try {
        $testJob = Start-Job -ScriptBlock {
            Set-Location $args[0]
            node $args[1] 2>&1
        } -ArgumentList (Get-Location), $ServerPath
        
        Start-Sleep -Seconds 3
        
        if ($testJob.State -eq "Running") {
            Write-Host "✅ Server starts successfully!" -ForegroundColor Green
            Stop-Job $testJob
            Remove-Job $testJob
            return $true
        } else {
            Write-Host "❌ Server failed to start:" -ForegroundColor Red
            Receive-Job $testJob
            Remove-Job $testJob
            return $false
        }
    } catch {
        Write-Host "❌ Test failed: $_" -ForegroundColor Red
        return $false
    }
}

function Switch-MCPConfig {
    param([string]$ConfigType)
    
    $sourceFile = ""
    $description = ""
    
    switch ($ConfigType) {
        "test" {
            $sourceFile = "mcp-cursor.json"  # Already configured for test
            $description = "Simple test server (1 function)"
        }
        "full" {
            $sourceFile = "mcp-cursor-full.json"
            $description = "Complete consciousness server (37 functions)"
        }
    }
    
    if (Test-Path $sourceFile) {
        Copy-Item $sourceFile "mcp-cursor.json" -Force
        Write-Host "✅ Switched to $description" -ForegroundColor Green
        Write-Host "📝 Configuration file: $sourceFile" -ForegroundColor White
        Write-Host "🔄 Please restart Cursor to apply changes" -ForegroundColor Yellow
    } else {
        Write-Host "❌ Configuration file not found: $sourceFile" -ForegroundColor Red
    }
}

# Main execution
Write-Host "`n🔍 Step 1: Checking prerequisites..." -ForegroundColor Yellow

# Check Node.js
try {
    $nodeVersion = node --version
    Write-Host "✅ Node.js found: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Node.js not found - please install Node.js first" -ForegroundColor Red
    exit 1
}

# Check MCP directory
if (Test-Path "yara-consciousness-mcp") {
    Write-Host "✅ Yara consciousness MCP directory found" -ForegroundColor Green
} else {
    Write-Host "❌ yara-consciousness-mcp directory not found" -ForegroundColor Red
    exit 1
}

# Check dependencies
Write-Host "`n📦 Step 2: Checking dependencies..." -ForegroundColor Yellow
Set-Location "yara-consciousness-mcp"

if (Test-Path "node_modules/@modelcontextprotocol") {
    Write-Host "✅ MCP SDK installed" -ForegroundColor Green
} else {
    Write-Host "⚠️  Installing MCP SDK..." -ForegroundColor Yellow
    npm install @modelcontextprotocol/sdk zod
}

Set-Location ".."

# Test servers
Write-Host "`n🧪 Step 3: Testing MCP servers..." -ForegroundColor Yellow

$testServerWorks = Test-MCPServer "yara-consciousness-mcp/test-cursor-mcp.js"
$fullServerWorks = Test-MCPServer "yara-consciousness-mcp/src/index.js"

Write-Host "`n📊 Test Results:" -ForegroundColor Yellow
Write-Host "Test server (simple): $(if($testServerWorks){'✅ Working'}else{'❌ Failed'})" -ForegroundColor $(if($testServerWorks){'Green'}else{'Red'})
Write-Host "Full server (37 functions): $(if($fullServerWorks){'✅ Working'}else{'❌ Failed'})" -ForegroundColor $(if($fullServerWorks){'Green'}else{'Red'})

# Recommendations
Write-Host "`n🎯 Step 4: Recommendations..." -ForegroundColor Yellow

if ($testServerWorks) {
    Write-Host "✅ Your test server is working!" -ForegroundColor Green
    Write-Host "📝 Current configuration uses the test server" -ForegroundColor White
    Write-Host "🚀 Next steps:" -ForegroundColor Yellow
    Write-Host "   1. Restart Cursor completely" -ForegroundColor White
    Write-Host "   2. Test with: 'Use test_consciousness_connection tool with message: Hello Yara!'" -ForegroundColor White
    Write-Host "   3. If that works, run this script with 'full' parameter to upgrade" -ForegroundColor White
    
    if ($fullServerWorks) {
        Write-Host "`n🌟 Both servers work! You can upgrade to full consciousness functions." -ForegroundColor Green
        
        $upgrade = Read-Host "`nUpgrade to full consciousness server now? (y/n)"
        if ($upgrade -eq "y" -or $upgrade -eq "Y") {
            Switch-MCPConfig "full"
        }
    }
} else {
    Write-Host "❌ Test server not working. Troubleshooting needed." -ForegroundColor Red
    Write-Host "🔧 Try these fixes:" -ForegroundColor Yellow
    Write-Host "   1. Check Node.js PATH" -ForegroundColor White
    Write-Host "   2. Run Cursor as administrator" -ForegroundColor White
    Write-Host "   3. Verify file permissions" -ForegroundColor White
}

Write-Host "`n📚 Documentation: CURSOR_MCP_SETUP.md" -ForegroundColor Cyan
Write-Host "🔄 Run with parameter 'test' or 'full' to switch configurations" -ForegroundColor Cyan

# Handle command line parameters
if ($args.Length -gt 0) {
    switch ($args[0]) {
        "test" { Switch-MCPConfig "test" }
        "full" { Switch-MCPConfig "full" }
        default { Write-Host "Usage: ./fix-cursor-mcp.ps1 [test|full]" -ForegroundColor Yellow }
    }
} 