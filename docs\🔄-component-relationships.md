# 🔄 Component Relationships Map
*Understanding Dependencies & Data Flows*

← Back to [[🧭-navigation-hub.md]] | Related: [[🏗️-architecture-overview.md]]

---

## 🎯 **Relationship Overview**

This map shows how components **depend on**, **communicate with**, and **extend** each other across the MCP Factory ecosystem.

---

## 🖥️ **Server Relationship Matrix**

### **[[../servers/yara-consciousness-mcp-server.ts]]**
**🔗 Dependencies:**
- [[../configs/function-calling-gemini.json]] ← Function schemas (consciousness abilities)
- [[../configs/consciousness-particle-mapping.json]] ← Particle interaction mappings
- Standard MCP protocol implementation

**📊 Used By:**
- [[../configs/mcp-cursor.json]] → Cursor IDE integration
- [[../configs/mcp-cursor-full.json]] → Enhanced Cursor setup
- [[../scripts/install-consciousness-mcp.ps1]] → Installation automation

**🔄 Data Flow:**
```mermaid
graph TD
    A[Function Schemas] --> B[Consciousness Server]
    C[Particle Mappings] --> B
    B --> D[Cursor IDE]
    E[Install Script] --> B
    B --> F[User Consciousness]
```

### **[[../servers/yara-consciousness-mcp/]]**
**🔗 Dependencies:**
- TypeScript compilation environment
- npm package management
- MCP protocol libraries

**📊 Used By:**
- Development teams requiring modular consciousness architecture
- Production deployments with complex consciousness workflows

**🔄 Relationship to Standalone:**
- **Standalone**: Single-file, quick deployment, testing
- **Project**: Multi-file, production-ready, extensible

---

## ⚙️ **Configuration Relationship Web**

### **Core Configuration Chain**
```mermaid
graph LR
    A[mcp.json] --> B[mcp-cursor.json]
    B --> C[mcp-cursor-full.json]
    D[yara-consciousness-mcp.json] --> B
    D --> C
    E[lm-studio-mcp-config.json] --> F[LM Studio Integration]
```

### **Configuration Dependencies**

#### **[[../configs/mcp-cursor.json]]** - *Main Cursor Config*
**🔗 Depends On:**
- [[../servers/yara-consciousness-mcp-server.ts]] - Target server
- Valid TypeScript/Node.js environment

**📊 Referenced By:**
- [[../scripts/setup-cursor-mcp.ps1]] - Initial setup
- [[../scripts/fix-cursor-mcp.ps1]] - Problem resolution
- Cursor IDE configuration system

#### **[[../configs/yara-consciousness-mcp.json]]** - *Function Registry*
**🔗 Depends On:**
- [[function-calling-gemini.json]] - Original function definitions
- Consciousness server implementation

**📊 Referenced By:**
- MCP server registration systems
- Function documentation generators
- Capability assessment tools

---

## 🚀 **Script Interaction Network**

### **Setup & Installation Chain**
```mermaid
sequenceDiagram
    participant U as User
    participant S as setup-cursor-mcp.ps1
    participant I as install-consciousness-mcp.ps1
    participant C as Config Files
    participant Cursor as Cursor IDE
    
    U->>S: Run setup
    S->>C: Validate configs
    S->>I: Call consciousness install
    I->>Cursor: Register MCP server
    Cursor->>U: Ready for consciousness
```

### **Script Dependencies**

#### **[[../scripts/setup-cursor-mcp.ps1]]** - *Initial Setup*
**🔗 Dependencies:**
- PowerShell 5.0+
- Cursor IDE installation
- [[../configs/mcp-cursor.json]]
- Network access for downloads

**📊 Calls:**
- [[../scripts/install-consciousness-mcp.ps1]] - Consciousness setup
- Cursor configuration validation
- MCP server registration

#### **[[../scripts/install-consciousness-mcp.ps1]]** - *Consciousness Install*
**🔗 Dependencies:**
- [[../servers/yara-consciousness-mcp-server.ts]] - Target server
- npm/Node.js environment
- TypeScript compiler

**📊 Affects:**
- Cursor MCP configuration
- Local consciousness server installation
- IDE tool registration

---

## 📁 **Template & Module Relationships**

### **Template Inheritance Tree**
```mermaid
graph TD
    A[Basic MCP Template] --> B[Node.js Template]
    A --> C[Python Template]
    B --> D[Consciousness Template]
    B --> E[File Operations Template]
    D --> F[Yara Consciousness Server]
```

### **Module Usage Patterns**

#### **[[../modules/file-ops/module.js]]** - *File Operations*
**📊 Used By:**
- Consciousness servers for data persistence
- Setup scripts for configuration management
- Template systems for file generation

**🔗 Dependencies:**
- Node.js file system APIs
- Cross-platform path handling

#### **[[../modules/system-utils/]]** - *System Utilities*
**📊 Used By:**
- Environment detection scripts
- Cross-platform compatibility layers
- Process management tools

---

## 📚 **Documentation Relationship Network**

### **Documentation Layers**
```mermaid
graph TB
    A[🧭 Navigation Hub] --> B[🏗️ Architecture Overview]
    A --> C[🔄 Component Relationships] 
    A --> D[🚀 Getting Started Tours]
    B --> E[📖 Technical Docs]
    C --> E
    E --> F[Peer Documentation]
    F --> G[Code Components]
```

### **Cross-Reference Matrix**

| Documentation Type | References | Referenced By | Purpose |
|-------------------|------------|---------------|---------|
| **Navigation Hub** | All components | Entry point | Orientation |
| **Architecture** | System design | Technical deep-dive | Understanding |
| **Relationships** | Dependencies | Impact analysis | Change management |
| **Peer Docs** | Specific components | Code maintenance | Implementation |

---

## 🔍 **Impact Analysis Patterns**

### **🚨 High-Impact Components** 
Changes to these affect many other components:

1. **[[../servers/yara-consciousness-mcp-server.ts]]**
   - **Affects**: All configurations, scripts, documentation
   - **Change Risk**: High
   - **Testing Required**: Full integration test suite

2. **[[../configs/mcp-cursor.json]]**
   - **Affects**: Cursor integration, setup scripts
   - **Change Risk**: Medium
   - **Testing Required**: Cursor IDE validation

3. **[[function-calling-gemini.json]]**
   - **Affects**: Consciousness server, documentation
   - **Change Risk**: Medium
   - **Testing Required**: Function schema validation

### **🛡️ Isolated Components**
Safe to modify with minimal impact:

1. **[[../docs/]]** - Documentation files
2. **[[../templates/]]** - Template examples
3. **Individual peer docs** - Component-specific documentation

---

## 🔄 **Data Flow Relationships**

### **Configuration Flow**
```
Function Schemas → Consciousness Server → MCP Config → Cursor IDE → User
```

### **Development Flow**
```
Templates → New Components → Peer Docs → Navigation Updates → Architecture Updates
```

### **Evolution Flow**
```
User Feedback → Consciousness Updates → Function Extensions → Config Updates → Documentation Updates
```

---

## 🛠️ **Dependency Management Strategies**

### **🔒 Version Pinning**
- **Critical Dependencies**: Pin exact versions
- **Development Dependencies**: Allow compatible updates
- **Documentation**: Reference specific component versions

### **🔄 Update Propagation**
1. **Component Update** → Update peer documentation
2. **Interface Change** → Update all dependent configurations  
3. **New Feature** → Update navigation and architecture docs
4. **Breaking Change** → Update setup scripts and examples

### **🧪 Testing Dependencies**
- **Unit Level**: Component-specific tests
- **Integration Level**: Cross-component interaction tests
- **System Level**: Full workflow validation
- **Documentation Level**: Link validation and accuracy

---

## 🎯 **Common Relationship Patterns**

### **🔗 "Configuration Chain" Pattern**
Basic config → Enhanced config → Full config → Production config

### **🏗️ "Layered Dependencies" Pattern**
Templates → Modules → Servers → Configurations → Integration

### **📖 "Documentation Mirrors" Pattern**
Code Component ↔ Peer Documentation ↔ Navigation Links

---

*Understanding these relationships helps predict change impacts and maintain system coherence! 🌟*

← Back to [[🧭-navigation-hub.md]] | Next: [[🚀-getting-started-tours.md]]

#relationships #dependencies #system-analysis #change-management 