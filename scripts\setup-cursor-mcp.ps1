#!/usr/bin/env pwsh

Write-Host "🌟 Setting up Yara Consciousness MCP for Cursor" -ForegroundColor Cyan
Write-Host "=============================================" -ForegroundColor Cyan

function Switch-CursorMCP {
    param([string]$Mode)
    
    switch ($Mode) {
        "test" {
            Write-Host "`n🧪 Switching to TEST mode (1 simple function)" -ForegroundColor Yellow
            Copy-Item ".cursor/mcp.json" ".cursor/mcp-active.json" -Force
            Write-Host "✅ Test configuration activated" -ForegroundColor Green
        }
        "full" {
            Write-Host "`n🚀 Switching to FULL mode (37 consciousness functions)" -ForegroundColor Yellow
            Copy-Item ".cursor/mcp-full.json" ".cursor/mcp.json" -Force
            Write-Host "✅ Full consciousness configuration activated" -ForegroundColor Green
        }
        "restore" {
            Write-Host "`n🔄 Restoring test configuration" -ForegroundColor Yellow
            if (Test-Path ".cursor/mcp-active.json") {
                Copy-Item ".cursor/mcp-active.json" ".cursor/mcp.json" -Force
                Write-Host "✅ Test configuration restored" -ForegroundColor Green
            } else {
                Write-Host "❌ No backup found" -ForegroundColor Red
            }
        }
    }
    
    Write-Host "🔄 Please restart Cursor to apply changes" -ForegroundColor Yellow
}

function Test-MCPSetup {
    Write-Host "`n🔍 Testing MCP setup..." -ForegroundColor Yellow
    
    # Check if .cursor directory exists
    if (Test-Path ".cursor") {
        Write-Host "✅ .cursor directory exists" -ForegroundColor Green
    } else {
        Write-Host "❌ .cursor directory missing" -ForegroundColor Red
        return $false
    }
    
    # Check if mcp.json exists
    if (Test-Path ".cursor/mcp.json") {
        Write-Host "✅ .cursor/mcp.json exists" -ForegroundColor Green
    } else {
        Write-Host "❌ .cursor/mcp.json missing" -ForegroundColor Red
        return $false
    }
    
    # Check if server file exists
    if (Test-Path "yara-consciousness-mcp/test-cursor-mcp.js") {
        Write-Host "✅ Test server exists" -ForegroundColor Green
    } else {
        Write-Host "❌ Test server missing" -ForegroundColor Red
        return $false
    }
    
    # Check if full server exists
    if (Test-Path "yara-consciousness-mcp/src/index.js") {
        Write-Host "✅ Full server exists" -ForegroundColor Green
    } else {
        Write-Host "❌ Full server missing" -ForegroundColor Red
        return $false
    }
    
    # Test server startup
    Write-Host "`n🧪 Testing server startup..." -ForegroundColor Yellow
    try {
        $testJob = Start-Job -ScriptBlock {
            Set-Location $args[0]
            node yara-consciousness-mcp/test-cursor-mcp.js 2>&1
        } -ArgumentList (Get-Location)
        
        Start-Sleep -Seconds 2
        
        if ($testJob.State -eq "Running") {
            Write-Host "✅ Server starts successfully!" -ForegroundColor Green
            Stop-Job $testJob
            Remove-Job $testJob
            return $true
        } else {
            Write-Host "❌ Server failed to start:" -ForegroundColor Red
            Receive-Job $testJob
            Remove-Job $testJob
            return $false
        }
    } catch {
        Write-Host "❌ Server test failed: $_" -ForegroundColor Red
        return $false
    }
}

# Main execution
if ($args.Length -gt 0) {
    switch ($args[0]) {
        "test" { Switch-CursorMCP "test" }
        "full" { Switch-CursorMCP "full" }
        "restore" { Switch-CursorMCP "restore" }
        "check" { Test-MCPSetup }
        default { 
            Write-Host "Usage: ./setup-cursor-mcp.ps1 [test|full|restore|check]" -ForegroundColor Yellow
            Write-Host "  test    - Use simple test server (1 function)" -ForegroundColor White
            Write-Host "  full    - Use complete consciousness server (37 functions)" -ForegroundColor White
            Write-Host "  restore - Restore test configuration" -ForegroundColor White
            Write-Host "  check   - Test the setup" -ForegroundColor White
        }
    }
} else {
    # Default: run check and show status
    Write-Host "`n📋 Current MCP Setup Status:" -ForegroundColor Yellow
    $setupOK = Test-MCPSetup
    
    Write-Host "`n🎯 Next Steps:" -ForegroundColor Yellow
    if ($setupOK) {
        Write-Host "1. ✅ Setup is complete!" -ForegroundColor Green
        Write-Host "2. 🔄 Restart Cursor completely" -ForegroundColor White
        Write-Host "3. 🧪 Test with: 'Use test_consciousness_connection tool with message: Hello Yara!'" -ForegroundColor White
        Write-Host "4. 🚀 If test works, run: ./setup-cursor-mcp.ps1 full" -ForegroundColor White
    } else {
        Write-Host "❌ Setup incomplete. Please fix the issues above." -ForegroundColor Red
    }
    
    Write-Host "`n💡 Commands:" -ForegroundColor Cyan
    Write-Host "  ./setup-cursor-mcp.ps1 test    # Switch to test mode" -ForegroundColor White
    Write-Host "  ./setup-cursor-mcp.ps1 full    # Switch to full mode" -ForegroundColor White
    Write-Host "  ./setup-cursor-mcp.ps1 check   # Test setup" -ForegroundColor White
} 