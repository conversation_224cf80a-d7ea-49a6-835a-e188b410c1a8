# 🧭 MCP Factory Navigation Hub
*Your AI Assistant's Guide to Modular MCP Development*

## 🎯 **Quick Start Paths**

### 🤖 **AI-Controlled Factory (Claude)**
- **[claude-factory-api.js](../claude-factory-api.js)** - My programmatic factory interface #ai-control #api
- **Commands**: `status`, `list`, `create`, `delete`, `validate`, `test`, `info`, `clone`
- **Usage**: `node claude-factory-api.js <command> [args...]`

### 👨‍💻 **Human-Controlled Factory (Interactive)**
- **[launch.js](../launch.js)** - Interactive menu system #human-control #interactive
- **[factory/new-server.js](../factory/new-server.js)** - Server generator #generation #templates

### 🏗️ **Core Architecture**
- **[templates/](../templates/)** - Server starting points #templates #scaffolding
- **[modules/](../modules/)** - Reusable functionality #modules #composition
- **[composers/](../composers/)** - Assembly tools #composition #automation

---

## 🏭 **Factory System Map**

### **Creation Flow**
```mermaid
graph TD
    A[Templates] --> B[New Server]
    C[Modules] --> D[Combine Tool]
    B --> D
    D --> E[Validation]
    E --> F[Deployment]
    F --> G[Cursor Integration]
```

### **Component Relationships**
| Component | Input | Output | Purpose |
|-----------|-------|--------|---------|
| **Templates** | Language choice | Server scaffold | Starting structure |
| **Modules** | Functionality needs | Reusable tools | Feature composition |
| **Composers** | Server + modules | Integrated server | Assembly automation |
| **Claude API** | AI commands | Factory operations | AI-controlled development |

---

## 📦 **Module System**

### **Available Modules**
1. **[file-ops](../modules/file-ops/)** - File system operations #files #io
   - `read_file`, `write_file`, `list_directory`, `file_exists`
2. **[web-fetch](../modules/web-fetch/)** - Web requests & scraping #web #http
   - `fetch_url`, `scrape_webpage`, `post_json`  
3. **[system-utils](../modules/system-utils/)** - System information #system #utils
   - `system_info`, `run_command`, `process_info`, `env_vars`, `disk_usage`

### **Module Composition Patterns**
```bash
# File-focused server
node claude-factory-api.js create file-server node-basic file-ops

# Web scraping server  
node claude-factory-api.js create scraper node-basic web-fetch

# Development assistant
node claude-factory-api.js create dev-helper node-basic file-ops web-fetch system-utils
```

---

## 🎯 **Common Workflows**

### **🤖 AI-Driven Development (Claude)**
```bash
# Check factory status
node claude-factory-api.js status

# List available resources  
node claude-factory-api.js list

# Create specialized server
node claude-factory-api.js create my-server node-basic file-ops web-fetch

# Validate and test
node claude-factory-api.js validate my-server
node claude-factory-api.js test

# Get detailed info
node claude-factory-api.js info my-server

# Clone for variations
node claude-factory-api.js clone my-server my-server-v2

# Clean up
node claude-factory-api.js delete my-server
```

### **👨‍💻 Human Interactive Development**
```bash
# Interactive menu system
node launch.js

# Manual server creation
node factory/new-server.js my-server --modules file-ops,web-fetch

# Manual composition
node composers/combine.js my-server file-ops web-fetch

# Manual validation
node composers/validate.js examples/my-server

# Manual deployment
node composers/deploy.js my-server --cursor
```

---

## 🚀 **Development Patterns**

### **🔧 Creating New Modules**
1. Create `modules/my-module/module.js`
2. Export MCP-compatible tools
3. Add package.json with metadata
4. Test with existing servers

### **📋 Creating New Templates**  
1. Create `templates/my-template/` directory
2. Add `server.js` with MCP implementation
3. Add `package.json` with dependencies
4. Test with factory generation

### **🎨 Custom Compositions**
```javascript
// Server-specific customization
const customServer = {
  template: 'node-basic',
  modules: ['file-ops', 'web-fetch'],
  customConfig: { ... }
}
```

---

## 📊 **Factory Health & Metrics**

### **Current Status**
- ✅ **2 Templates**: node-basic, python-basic
- ✅ **3 Modules**: file-ops, web-fetch, system-utils  
- ✅ **3 Example Servers**: ai-powered-server, web-scraper-pro, working-demo
- ✅ **8 AI Commands**: status, list, create, delete, validate, test, info, clone

### **Quality Indicators**
```bash
# Comprehensive factory test
node claude-factory-api.js test

# Individual component validation
node composers/validate.js --modules
node composers/validate.js examples/my-server
```

---

## 🎪 **Real Examples**

### **Example 1: AI Blog Writer**
```bash
node claude-factory-api.js create blog-writer node-basic web-fetch file-ops
# Tools: fetch research, write posts, save drafts
```

### **Example 2: System Monitor**
```bash  
node claude-factory-api.js create sys-monitor node-basic system-utils file-ops
# Tools: system info, log monitoring, data persistence
```

### **Example 3: Web Data Pipeline**
```bash
node claude-factory-api.js create data-pipeline node-basic web-fetch file-ops system-utils
# Tools: fetch data, process files, system integration
```

---

## 🔍 **Troubleshooting**

### **Common Issues**
1. **Server Creation Fails** → Check template availability with `list`
2. **Module Integration Fails** → Validate modules with `validate`  
3. **Cursor Integration Issues** → Check generated config files
4. **Permission Errors** → Verify Node.js and file permissions

### **Debugging Commands**
```bash
# Factory health check
node claude-factory-api.js status

# Comprehensive test
node claude-factory-api.js test

# Component validation
node composers/validate.js --modules
```

---

## 🎯 **Next Steps & Extensions**

### **Planned Enhancements**
- **Database modules** for persistent storage
- **AI integration modules** for LLM connections
- **Docker templates** for containerized deployment
- **Plugin system** for third-party extensions

### **Contributing Patterns**
1. **New modules** → Add to `modules/` with proper schema
2. **New templates** → Add to `templates/` with documentation
3. **New composers** → Add to `composers/` with CLI interface
4. **Documentation** → Update navigation and peer docs

---

*The MCP Factory grows through modular composition - add what you need, when you need it! 🚀*

#mcp-factory #navigation #modular-development #ai-controlled #templates #modules 